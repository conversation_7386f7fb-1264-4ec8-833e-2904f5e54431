"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.BiometricMimicry = void 0;
const electron_1 = require("electron");
const crypto = __importStar(require("crypto"));
class BiometricMimicry {
    constructor() {
        this.profiles = new Map();
        this.currentProfile = null;
        this.learningMode = true;
        this.adaptationHistory = [];
        this.biometricData = [];
        this.initializeBaseProfiles();
    }
    async initialize() {
        await this.loadBiometricProfiles();
        this.selectOptimalProfile();
        this.startBiometricLearning();
        console.log('Biometric Behavior Mimicry initialized');
    }
    initializeBaseProfiles() {
        const baseProfiles = [
            {
                id: 'professional_user',
                name: 'Professional User',
                biometricData: {
                    typingDynamics: {
                        keystrokeTimings: [80, 85, 90, 75, 95], // ms
                        interKeyIntervals: [120, 110, 130, 115, 125],
                        typingRhythm: {
                            burstLength: 8,
                            pauseLength: 500,
                            consistency: 0.8
                        },
                        errorPatterns: {
                            commonErrors: ['teh', 'adn', 'hte'],
                            correctionDelay: 400,
                            correctionMethod: 'backspace'
                        },
                        pressureDynamics: {
                            avgPressure: 0.7,
                            pressureVariance: 0.2,
                            releasePattern: 'quick'
                        }
                    },
                    mouseMovement: {
                        velocityProfile: {
                            avgVelocity: 180,
                            accelerationCurve: [0.3, 0.7, 1.0, 0.8, 0.5],
                            decelerationCurve: [1.0, 0.7, 0.4, 0.2, 0.0]
                        },
                        trajectoryCharacteristics: {
                            straightness: 0.85,
                            overshoot: 0.1,
                            tremor: 0.05
                        },
                        clickPatterns: {
                            clickDuration: 120,
                            doubleClickSpeed: 300,
                            dragBehavior: 'smooth'
                        },
                        scrollBehavior: {
                            scrollSpeed: 3,
                            scrollAcceleration: true,
                            scrollDirection: 'natural'
                        }
                    },
                    readingPattern: {
                        readingSpeed: 250,
                        scanningPattern: 'f_pattern',
                        focusAreas: {
                            headlines: 25,
                            images: 15,
                            text: 50,
                            navigation: 10
                        },
                        comprehensionIndicators: {
                            backtrackFrequency: 0.15,
                            pauseAtPunctuation: true,
                            skimmingBehavior: 0.3
                        }
                    },
                    navigationStyle: {
                        browsingPattern: 'goal_oriented',
                        tabUsage: {
                            maxTabs: 8,
                            tabSwitchFrequency: 0.4,
                            tabOrganization: 'grouped'
                        },
                        searchBehavior: {
                            queryLength: 4,
                            refinementPattern: 'iterative',
                            resultExamination: 'thorough'
                        },
                        bookmarkUsage: {
                            frequency: 0.6,
                            organization: 'folders',
                            revisitPattern: 'regular'
                        }
                    },
                    attentionSpan: {
                        sessionDuration: 45 * 60 * 1000, // 45 minutes
                        taskSwitchingRate: 0.2,
                        distractionSusceptibility: 0.3,
                        focusDepth: {
                            shallow: 20,
                            medium: 50,
                            deep: 30
                        },
                        breakPatterns: {
                            frequency: 0.25,
                            duration: 5 * 60 * 1000, // 5 minutes
                            triggers: ['fatigue', 'completion', 'distraction']
                        }
                    }
                },
                confidence: 0.9,
                lastUpdated: Date.now(),
                adaptationRate: 0.1
            },
            {
                id: 'casual_user',
                name: 'Casual User',
                biometricData: {
                    typingDynamics: {
                        keystrokeTimings: [150, 160, 140, 170, 155],
                        interKeyIntervals: [200, 180, 220, 190, 210],
                        typingRhythm: {
                            burstLength: 4,
                            pauseLength: 1000,
                            consistency: 0.5
                        },
                        errorPatterns: {
                            commonErrors: ['typo', 'mispelling', 'wrnog'],
                            correctionDelay: 800,
                            correctionMethod: 'select_replace'
                        },
                        pressureDynamics: {
                            avgPressure: 0.5,
                            pressureVariance: 0.4,
                            releasePattern: 'gradual'
                        }
                    },
                    mouseMovement: {
                        velocityProfile: {
                            avgVelocity: 120,
                            accelerationCurve: [0.2, 0.5, 0.8, 0.6, 0.3],
                            decelerationCurve: [0.8, 0.6, 0.4, 0.2, 0.1]
                        },
                        trajectoryCharacteristics: {
                            straightness: 0.6,
                            overshoot: 0.3,
                            tremor: 0.15
                        },
                        clickPatterns: {
                            clickDuration: 180,
                            doubleClickSpeed: 500,
                            dragBehavior: 'hesitant'
                        },
                        scrollBehavior: {
                            scrollSpeed: 2,
                            scrollAcceleration: false,
                            scrollDirection: 'traditional'
                        }
                    },
                    readingPattern: {
                        readingSpeed: 180,
                        scanningPattern: 'random',
                        focusAreas: {
                            headlines: 35,
                            images: 30,
                            text: 25,
                            navigation: 10
                        },
                        comprehensionIndicators: {
                            backtrackFrequency: 0.3,
                            pauseAtPunctuation: false,
                            skimmingBehavior: 0.7
                        }
                    },
                    navigationStyle: {
                        browsingPattern: 'exploratory',
                        tabUsage: {
                            maxTabs: 15,
                            tabSwitchFrequency: 0.8,
                            tabOrganization: 'random'
                        },
                        searchBehavior: {
                            queryLength: 2,
                            refinementPattern: 'single_shot',
                            resultExamination: 'quick_scan'
                        },
                        bookmarkUsage: {
                            frequency: 0.2,
                            organization: 'none',
                            revisitPattern: 'rare'
                        }
                    },
                    attentionSpan: {
                        sessionDuration: 20 * 60 * 1000, // 20 minutes
                        taskSwitchingRate: 0.7,
                        distractionSusceptibility: 0.8,
                        focusDepth: {
                            shallow: 60,
                            medium: 30,
                            deep: 10
                        },
                        breakPatterns: {
                            frequency: 0.5,
                            duration: 2 * 60 * 1000, // 2 minutes
                            triggers: ['boredom', 'notification', 'impulse']
                        }
                    }
                },
                confidence: 0.8,
                lastUpdated: Date.now(),
                adaptationRate: 0.2
            }
        ];
        baseProfiles.forEach(profile => {
            this.profiles.set(profile.id, profile);
        });
    }
    async loadBiometricProfiles() {
        // In a real implementation, this would load from secure storage
        // For now, we'll use the base profiles
        console.log(`Loaded ${this.profiles.size} biometric profiles`);
    }
    selectOptimalProfile() {
        // Select profile based on current context
        const timeOfDay = new Date().getHours();
        const dayOfWeek = new Date().getDay();
        // Business hours (9-17) on weekdays suggest professional user
        if (timeOfDay >= 9 && timeOfDay <= 17 && dayOfWeek >= 1 && dayOfWeek <= 5) {
            this.currentProfile = this.profiles.get('professional_user') || null;
        }
        else {
            this.currentProfile = this.profiles.get('casual_user') || null;
        }
        if (this.currentProfile) {
            console.log(`Selected biometric profile: ${this.currentProfile.name}`);
        }
    }
    startBiometricLearning() {
        // Start collecting biometric data for learning
        setInterval(() => {
            this.collectBiometricData();
        }, 10000); // Collect every 10 seconds
        // Adapt profile every 5 minutes
        setInterval(() => {
            this.adaptProfile();
        }, 300000);
    }
    collectBiometricData() {
        // Simulate collecting real biometric data
        const dataTypes = ['typing', 'mouse', 'reading', 'navigation', 'attention'];
        const dataType = dataTypes[Math.floor(Math.random() * dataTypes.length)];
        const data = this.generateSyntheticBiometricData(dataType);
        this.biometricData.push({
            timestamp: Date.now(),
            type: dataType,
            data
        });
        // Keep only recent data (last hour)
        const oneHourAgo = Date.now() - 3600000;
        this.biometricData = this.biometricData.filter(d => d.timestamp > oneHourAgo);
    }
    generateSyntheticBiometricData(type) {
        switch (type) {
            case 'typing':
                return {
                    keystrokeTime: 80 + Math.random() * 40,
                    interKeyInterval: 120 + Math.random() * 60,
                    pressure: 0.5 + Math.random() * 0.3
                };
            case 'mouse':
                return {
                    velocity: 100 + Math.random() * 100,
                    acceleration: Math.random(),
                    clickDuration: 100 + Math.random() * 100
                };
            case 'reading':
                return {
                    dwellTime: 1000 + Math.random() * 2000,
                    scanPattern: Math.random() > 0.5 ? 'linear' : 'jumping',
                    backtrack: Math.random() > 0.8
                };
            case 'navigation':
                return {
                    tabSwitches: Math.floor(Math.random() * 5),
                    searchQueries: Math.floor(Math.random() * 3),
                    bookmarkUse: Math.random() > 0.7
                };
            case 'attention':
                return {
                    focusLevel: Math.random(),
                    taskSwitches: Math.floor(Math.random() * 3),
                    breakTaken: Math.random() > 0.8
                };
            default:
                return {};
        }
    }
    adaptProfile() {
        if (!this.currentProfile || !this.learningMode)
            return;
        const recentData = this.biometricData.filter(d => Date.now() - d.timestamp < 600000 // Last 10 minutes
        );
        if (recentData.length < 10)
            return; // Need sufficient data
        const adaptations = this.analyzeAndAdapt(recentData);
        if (adaptations.length > 0) {
            this.applyAdaptations(adaptations);
            this.adaptationHistory.push({
                timestamp: Date.now(),
                changes: adaptations
            });
            console.log(`Profile adapted: ${adaptations.join(', ')}`);
        }
    }
    analyzeAndAdapt(data) {
        const adaptations = [];
        // Analyze typing patterns
        const typingData = data.filter(d => d.type === 'typing');
        if (typingData.length > 0) {
            const avgKeystrokeTime = typingData.reduce((sum, d) => sum + d.data.keystrokeTime, 0) / typingData.length;
            const currentAvg = this.currentProfile.biometricData.typingDynamics.keystrokeTimings.reduce((a, b) => a + b, 0) / 5;
            if (Math.abs(avgKeystrokeTime - currentAvg) > 20) {
                this.currentProfile.biometricData.typingDynamics.keystrokeTimings =
                    this.currentProfile.biometricData.typingDynamics.keystrokeTimings.map(t => t + (avgKeystrokeTime - currentAvg) * this.currentProfile.adaptationRate);
                adaptations.push('typing_speed');
            }
        }
        // Analyze mouse patterns
        const mouseData = data.filter(d => d.type === 'mouse');
        if (mouseData.length > 0) {
            const avgVelocity = mouseData.reduce((sum, d) => sum + d.data.velocity, 0) / mouseData.length;
            const currentVelocity = this.currentProfile.biometricData.mouseMovement.velocityProfile.avgVelocity;
            if (Math.abs(avgVelocity - currentVelocity) > 30) {
                this.currentProfile.biometricData.mouseMovement.velocityProfile.avgVelocity =
                    currentVelocity + (avgVelocity - currentVelocity) * this.currentProfile.adaptationRate;
                adaptations.push('mouse_velocity');
            }
        }
        // Analyze attention patterns
        const attentionData = data.filter(d => d.type === 'attention');
        if (attentionData.length > 0) {
            const avgFocus = attentionData.reduce((sum, d) => sum + d.data.focusLevel, 0) / attentionData.length;
            if (avgFocus < 0.3) {
                this.currentProfile.biometricData.attentionSpan.distractionSusceptibility += 0.1;
                adaptations.push('attention_span');
            }
            else if (avgFocus > 0.8) {
                this.currentProfile.biometricData.attentionSpan.distractionSusceptibility -= 0.1;
                adaptations.push('focus_improvement');
            }
        }
        return adaptations;
    }
    applyAdaptations(adaptations) {
        // Update profile confidence based on adaptations
        this.currentProfile.confidence = Math.min(0.95, this.currentProfile.confidence + 0.05);
        this.currentProfile.lastUpdated = Date.now();
    }
    simulateTyping(text) {
        if (!this.currentProfile)
            return;
        const typing = this.currentProfile.biometricData.typingDynamics;
        const windows = electron_1.BrowserWindow.getAllWindows();
        if (windows.length === 0)
            return;
        const window = windows[0];
        let charIndex = 0;
        const typeNextChar = () => {
            if (charIndex >= text.length)
                return;
            const char = text[charIndex];
            const keystrokeTime = typing.keystrokeTimings[charIndex % typing.keystrokeTimings.length];
            const interKeyInterval = typing.interKeyIntervals[charIndex % typing.interKeyIntervals.length];
            // Add some randomness based on consistency
            const variance = (1 - typing.typingRhythm.consistency) * 0.5;
            const actualKeystrokeTime = keystrokeTime * (1 + (Math.random() - 0.5) * variance);
            const actualInterval = interKeyInterval * (1 + (Math.random() - 0.5) * variance);
            window.webContents.executeJavaScript(`
                (function() {
                    const activeElement = document.activeElement;
                    if (activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA')) {
                        const keydownEvent = new KeyboardEvent('keydown', { key: '${char}', bubbles: true });
                        const keyupEvent = new KeyboardEvent('keyup', { key: '${char}', bubbles: true });
                        
                        activeElement.dispatchEvent(keydownEvent);
                        
                        setTimeout(() => {
                            activeElement.value += '${char}';
                            const inputEvent = new Event('input', { bubbles: true });
                            activeElement.dispatchEvent(inputEvent);
                            activeElement.dispatchEvent(keyupEvent);
                        }, ${actualKeystrokeTime});
                    }
                })();
            `).catch(() => {
                // Ignore errors
            });
            charIndex++;
            setTimeout(typeNextChar, actualInterval);
        };
        typeNextChar();
    }
    simulateMouseMovement(targetX, targetY) {
        if (!this.currentProfile)
            return;
        const mouse = this.currentProfile.biometricData.mouseMovement;
        const windows = electron_1.BrowserWindow.getAllWindows();
        if (windows.length === 0)
            return;
        const window = windows[0];
        const steps = 20;
        const movements = [];
        // Generate realistic mouse trajectory
        for (let i = 0; i <= steps; i++) {
            const progress = i / steps;
            const accelerationIndex = Math.floor(progress * (mouse.velocityProfile.accelerationCurve.length - 1));
            const acceleration = mouse.velocityProfile.accelerationCurve[accelerationIndex];
            // Add trajectory characteristics
            const straightness = mouse.trajectoryCharacteristics.straightness;
            const tremor = mouse.trajectoryCharacteristics.tremor;
            const x = targetX * progress + (Math.random() - 0.5) * (1 - straightness) * 50 + (Math.random() - 0.5) * tremor * 5;
            const y = targetY * progress + (Math.random() - 0.5) * (1 - straightness) * 50 + (Math.random() - 0.5) * tremor * 5;
            const delay = (1000 / mouse.velocityProfile.avgVelocity) * acceleration * 10;
            movements.push({ x: Math.floor(x), y: Math.floor(y), delay });
        }
        // Execute mouse movements
        let stepIndex = 0;
        const executeMovement = () => {
            if (stepIndex >= movements.length)
                return;
            const movement = movements[stepIndex];
            window.webContents.executeJavaScript(`
                (function() {
                    const event = new MouseEvent('mousemove', {
                        clientX: ${movement.x},
                        clientY: ${movement.y},
                        bubbles: true
                    });
                    document.dispatchEvent(event);
                })();
            `).catch(() => {
                // Ignore errors
            });
            stepIndex++;
            setTimeout(executeMovement, movement.delay);
        };
        executeMovement();
    }
    getCurrentProfile() {
        return this.currentProfile ? { ...this.currentProfile } : null;
    }
    switchProfile(profileId) {
        const profile = this.profiles.get(profileId);
        if (profile) {
            this.currentProfile = profile;
            console.log(`Switched to biometric profile: ${profile.name}`);
            return true;
        }
        return false;
    }
    createCustomProfile(name, biometricData) {
        const profileId = crypto.randomBytes(8).toString('hex');
        const profile = {
            id: profileId,
            name,
            biometricData,
            confidence: 0.5,
            lastUpdated: Date.now(),
            adaptationRate: 0.15
        };
        this.profiles.set(profileId, profile);
        return profileId;
    }
    getProfileStatistics() {
        return {
            totalProfiles: this.profiles.size,
            currentProfile: this.currentProfile?.name || 'None',
            adaptationCount: this.adaptationHistory.length,
            learningMode: this.learningMode,
            dataPoints: this.biometricData.length
        };
    }
    enableLearningMode() {
        this.learningMode = true;
        console.log('Biometric learning mode enabled');
    }
    disableLearningMode() {
        this.learningMode = false;
        console.log('Biometric learning mode disabled');
    }
}
exports.BiometricMimicry = BiometricMimicry;
//# sourceMappingURL=BiometricMimicry.js.map