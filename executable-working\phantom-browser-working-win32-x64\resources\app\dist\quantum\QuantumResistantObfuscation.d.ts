import { EventEmitter } from 'events';
export interface QuantumResistantConfig {
    enablePostQuantumCrypto: boolean;
    enableQuantumKeyDistribution: boolean;
    enableHybridMode: boolean;
    algorithmSuite: 'kyber' | 'dilithium' | 'sphincs' | 'hybrid';
    securityLevel: 1 | 3 | 5;
    keyRotationInterval: number;
    quantumChannelSimulation: boolean;
}
export interface PostQuantumKeyPair {
    publicKey: Buffer;
    privateKey: Buffer;
    algorithm: string;
    securityLevel: number;
    keySize: number;
    createdAt: number;
    expiresAt: number;
}
export interface QuantumChannel {
    id: string;
    participants: string[];
    sharedSecret: Buffer;
    errorRate: number;
    securityParameter: number;
    isAuthenticated: boolean;
    lastUsed: number;
}
export interface QuantumObfuscationMetrics {
    keyExchanges: number;
    encryptionOperations: number;
    quantumChannelUses: number;
    averageKeySize: number;
    securityLevel: number;
    quantumResistanceScore: number;
}
export declare class QuantumResistantObfuscation extends EventEmitter {
    private config;
    private keyPairs;
    private quantumChannels;
    private metrics;
    private keyRotationTimer;
    private isInitialized;
    constructor();
    initialize(): Promise<void>;
    private generateInitialKeyPairs;
    private generatePostQuantumKeyPair;
    private derivePublicKey;
    private latticeBasedDerivation;
    private moduleLWEDerivation;
    private hashBasedDerivation;
    private initializeQuantumKeyDistribution;
    performQuantumKeyExchange(peerId: string): Promise<Buffer>;
    private simulateBB84Protocol;
    private simulateQuantumTransmission;
    private errorCorrection;
    private calculateParity;
    private privacyAmplification;
    encryptWithQuantumResistance(data: Buffer, algorithm?: string): Promise<Buffer>;
    private encapsulateKey;
    private kyberEncapsulate;
    private dilithiumSign;
    private sphincsSign;
    private selectOptimalAlgorithm;
    private startKeyRotation;
    private rotateKeys;
    private cleanupExpiredChannels;
    private updateQuantumResistanceScore;
    private calculateAverageKeyAge;
    getStatus(): {
        isInitialized: boolean;
        config: QuantumResistantConfig;
        keyPairs: number;
        quantumChannels: number;
        metrics: QuantumObfuscationMetrics;
        averageKeyAge: number;
    };
    updateConfig(newConfig: Partial<QuantumResistantConfig>): void;
    destroy(): void;
}
//# sourceMappingURL=QuantumResistantObfuscation.d.ts.map