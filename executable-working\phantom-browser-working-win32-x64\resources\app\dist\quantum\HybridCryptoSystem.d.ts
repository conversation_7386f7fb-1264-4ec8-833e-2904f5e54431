export interface HybridKeyPair {
    classical: {
        publicKey: Buffer;
        privateKey: Buffer;
        algorithm: 'rsa' | 'ecdsa' | 'ecdh';
    };
    postQuantum: {
        publicKey: Buffer;
        privateKey: Buffer;
        algorithm: 'kyber' | 'dilithium' | 'sphincs';
    };
    combined: {
        publicKey: Buffer;
        fingerprint: string;
    };
}
export interface HybridEncryptionResult {
    classicalCiphertext: Buffer;
    postQuantumCiphertext: Buffer;
    combinedKey: Buffer;
    metadata: {
        algorithms: string[];
        keySize: number;
        timestamp: number;
        securityLevel: number;
    };
}
export interface CryptoMigrationPlan {
    phase: 'classical' | 'hybrid' | 'post_quantum';
    timeline: number;
    algorithms: {
        current: string[];
        target: string[];
        deprecated: string[];
    };
    migrationStrategy: 'gradual' | 'immediate' | 'conditional';
}
export interface SecurityAssessment {
    quantumThreat: 'none' | 'low' | 'medium' | 'high' | 'critical';
    classicalSecurity: number;
    postQuantumSecurity: number;
    overallSecurity: number;
    recommendations: string[];
    nextReview: number;
}
export declare class HybridCryptoSystem {
    private qkd;
    private migrationPlan;
    private securityAssessment;
    private keyCache;
    private performanceMetrics;
    constructor();
    initialize(): Promise<void>;
    generateHybridKeyPair(securityLevel?: 1 | 3 | 5): Promise<HybridKeyPair>;
    private generateClassicalKeyPair;
    private generatePostQuantumKeyPair;
    private combinePublicKeys;
    private calculateFingerprint;
    hybridEncrypt(data: Buffer, recipientPublicKey: HybridKeyPair['combined']['publicKey']): Promise<HybridEncryptionResult>;
    hybridDecrypt(encryptionResult: HybridEncryptionResult, privateKeyPair: HybridKeyPair): Promise<Buffer>;
    private parseHybridPublicKey;
    private classicalEncrypt;
    private postQuantumEncrypt;
    private combineEncryptedKeys;
    private classicalDecrypt;
    private postQuantumDecrypt;
    performQuantumKeyExchange(peerId: string): Promise<Buffer>;
    private performSecurityAssessment;
    private assessQuantumThreat;
    private calculateClassicalSecurity;
    private calculatePostQuantumSecurity;
    private generateSecurityRecommendations;
    private updatePerformanceMetrics;
    getStatus(): {
        migrationPlan: CryptoMigrationPlan;
        securityAssessment: SecurityAssessment;
        performanceMetrics: typeof this.performanceMetrics;
        cachedKeys: number;
    };
    updateMigrationPlan(plan: Partial<CryptoMigrationPlan>): void;
    destroy(): void;
}
//# sourceMappingURL=HybridCryptoSystem.d.ts.map