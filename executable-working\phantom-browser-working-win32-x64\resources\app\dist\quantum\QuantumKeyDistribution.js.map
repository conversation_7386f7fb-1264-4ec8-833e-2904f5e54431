{"version": 3, "file": "QuantumKeyDistribution.js", "sourceRoot": "", "sources": ["../../src/quantum/QuantumKeyDistribution.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,mCAAsC;AA0CtC,MAAa,sBAAuB,SAAQ,qBAAY;IAMpD;QACI,KAAK,EAAE,CAAC;QANJ,aAAQ,GAA6B,IAAI,GAAG,EAAE,CAAC;QAC/C,aAAQ,GAAgC,IAAI,GAAG,EAAE,CAAC;QAElD,aAAQ,GAAY,KAAK,CAAC;QAI9B,IAAI,CAAC,UAAU,GAAG;YACd,iBAAiB,EAAE,CAAC;YACpB,oBAAoB,EAAE,CAAC;YACvB,gBAAgB,EAAE,CAAC;YACnB,qBAAqB,EAAE,CAAC;YACxB,iBAAiB,EAAE,CAAC;YACpB,iBAAiB,EAAE,CAAC;SACvB,CAAC;IACN,CAAC;IAED,KAAK,CAAC,UAAU;QACZ,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;QAC/D,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAErB,iCAAiC;QACjC,MAAM,IAAI,CAAC,oBAAoB,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;QAE7D,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;QAC3D,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC7B,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,SAAiB,EAAE,YAAsB;QAChE,MAAM,OAAO,GAAmB;YAC5B,EAAE,EAAE,SAAS;YACb,YAAY;YACZ,UAAU,EAAE,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,EAAE,aAAa;YACtD,qBAAqB,EAAE,KAAK;YAC5B,iBAAiB,EAAE,CAAC;YACpB,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE;YACpB,eAAe,EAAE,IAAI;SACxB,CAAC;QAEF,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACtC,OAAO,CAAC,GAAG,CAAC,mBAAmB,SAAS,iBAAiB,YAAY,CAAC,MAAM,eAAe,CAAC,CAAC;QAE7F,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,KAAa,EAAE,GAAW,EAAE,YAAoB,GAAG;QACtE,MAAM,SAAS,GAAG,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAEzD,MAAM,OAAO,GAAgB;YACzB,EAAE,EAAE,SAAS;YACb,YAAY,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;YAC1B,MAAM,EAAE,cAAc;YACtB,MAAM,EAAE,EAAE;YACV,SAAS,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;YAC1B,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;YACzB,SAAS,EAAE,CAAC;YACZ,iBAAiB,EAAE,GAAG;YACtB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACxB,CAAC;QAEF,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACtC,OAAO,CAAC,GAAG,CAAC,yBAAyB,SAAS,YAAY,KAAK,QAAQ,GAAG,EAAE,CAAC,CAAC;QAE9E,+BAA+B;QAC/B,MAAM,IAAI,CAAC,0BAA0B,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QAC1D,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QACnC,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;QAC3C,MAAM,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC,CAAC;QAEhD,OAAO,CAAC,MAAM,GAAG,UAAU,CAAC;QAC5B,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAC/B,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;QAEtC,OAAO,SAAS,CAAC;IACrB,CAAC;IAEO,KAAK,CAAC,0BAA0B,CAAC,OAAoB,EAAE,SAAiB;QAC5E,OAAO,CAAC,GAAG,CAAC,8CAA8C,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;QACxE,OAAO,CAAC,MAAM,GAAG,cAAc,CAAC;QAEhC,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzC,MAAM,kBAAkB,GAAG,SAAS,GAAG,CAAC,CAAC,CAAC,2BAA2B;QAErE,wCAAwC;QACxC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,kBAAkB,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1C,MAAM,GAAG,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACrC,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;YACnE,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACxC,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,oBAAoB,IAAI,kBAAkB,CAAC;QAC3D,OAAO,CAAC,GAAG,CAAC,eAAe,kBAAkB,eAAe,CAAC,CAAC;IAClE,CAAC;IAEO,iBAAiB;QACrB,6CAA6C;QAC7C,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,UAAU,CAAC;QAE/D,gDAAgD;QAChD,IAAI,YAAoB,CAAC;QACzB,IAAI,KAAK,KAAK,aAAa,EAAE,CAAC;YAC1B,YAAY,GAAG,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,YAAY;QACrD,CAAC;aAAM,CAAC;YACJ,YAAY,GAAG,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,cAAc;QACzD,CAAC;QAED,OAAO;YACH,KAAK,EAAE,KAAc;YACrB,KAAK;YACL,YAAY;YACZ,QAAQ,EAAE,KAAK;YACf,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACxB,CAAC;IACN,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,GAAe,EAAE,OAAuB;QACrE,sDAAsD;QACtD,IAAI,cAAc,GAAG,EAAE,GAAG,GAAG,EAAE,CAAC;QAEhC,sBAAsB;QACtB,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC;YACrC,cAAc,CAAC,KAAK,GAAG,cAAc,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1D,cAAc,CAAC,YAAY,GAAG,CAAC,cAAc,CAAC,YAAY,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC;QAC3E,CAAC;QAED,mCAAmC;QACnC,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,KAAK,EAAE,CAAC,CAAC,+BAA+B;YACxD,OAAO,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;YACpE,OAAO,CAAC,qBAAqB,GAAG,IAAI,CAAC;YACrC,IAAI,CAAC,UAAU,CAAC,qBAAqB,EAAE,CAAC;YAExC,4CAA4C;YAC5C,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,CAAC;gBACtB,cAAc,CAAC,KAAK,GAAG,cAAc,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9D,CAAC;QACL,CAAC;QAED,yCAAyC;QACzC,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,UAAU,CAAC;QAClE,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;QAElE,OAAO,CAAC,iBAAiB,EAAE,CAAC;QAC5B,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE9B,OAAO,cAAc,CAAC;IAC1B,CAAC;IAEO,iBAAiB,CAAC,GAAe,EAAE,gBAA4C;QACnF,MAAM,WAAW,GAAG,EAAE,GAAG,GAAG,EAAE,CAAC;QAC/B,WAAW,CAAC,QAAQ,GAAG,IAAI,CAAC;QAE5B,0EAA0E;QAC1E,IAAI,gBAAgB,KAAK,GAAG,CAAC,KAAK,EAAE,CAAC;YACjC,yCAAyC;YACzC,OAAO,WAAW,CAAC;QACvB,CAAC;QAED,+DAA+D;QAC/D,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,WAAW,CAAC,KAAK,GAAG,gBAAgB,CAAC;QAErC,+CAA+C;QAC/C,IAAI,gBAAgB,KAAK,aAAa,EAAE,CAAC;YACrC,WAAW,CAAC,YAAY,GAAG,WAAW,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAChE,CAAC;aAAM,CAAC;YACJ,WAAW,CAAC,YAAY,GAAG,WAAW,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;QAClE,CAAC;QAED,OAAO,WAAW,CAAC;IACvB,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,OAAoB;QAC7C,OAAO,CAAC,GAAG,CAAC,wCAAwC,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;QAClE,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC;QAE3B,MAAM,UAAU,GAAa,EAAE,CAAC;QAChC,IAAI,aAAa,GAAG,CAAC,CAAC;QAEtB,qDAAqD;QACrD,KAAK,MAAM,GAAG,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YAC/B,6FAA6F;YAC7F,MAAM,UAAU,GAAG,GAAG,CAAC,KAAK,CAAC;YAC7B,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,UAAU,CAAC;YAElE,IAAI,UAAU,KAAK,QAAQ,EAAE,CAAC;gBAC1B,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;gBAC3B,aAAa,EAAE,CAAC;YACpB,CAAC;QACL,CAAC;QAED,+BAA+B;QAC/B,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACrD,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;QAE9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACzC,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACpC,MAAM,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;YACvB,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;gBACtB,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC;YACpD,CAAC;QACL,CAAC;QAED,MAAM,iBAAiB,GAAG,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC;QAChE,OAAO,CAAC,GAAG,CAAC,qBAAqB,UAAU,CAAC,MAAM,cAAc,OAAO,CAAC,MAAM,CAAC,MAAM,iBAAiB,CAAC,iBAAiB,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC;IAC/J,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,OAAoB;QACrD,OAAO,CAAC,GAAG,CAAC,2CAA2C,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;QACrE,OAAO,CAAC,MAAM,GAAG,kBAAkB,CAAC;QAEpC,wCAAwC;QACxC,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC9D,IAAI,MAAM,GAAG,CAAC,CAAC;QAEf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;YAClC,mFAAmF;YACnF,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,EAAE,CAAC,CAAC,uBAAuB;gBAC/C,MAAM,EAAE,CAAC;YACb,CAAC;QACL,CAAC;QAED,OAAO,CAAC,SAAS,GAAG,MAAM,GAAG,UAAU,CAAC;QACxC,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,OAAO,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAE9E,oCAAoC;QACpC,IAAI,OAAO,CAAC,SAAS,GAAG,IAAI,EAAE,CAAC,CAAC,uBAAuB;YACnD,OAAO,CAAC,KAAK,CAAC,uDAAuD,CAAC,CAAC;YACvE,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC;YAC3B,OAAO;QACX,CAAC;QAED,+DAA+D;QAC/D,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;QAC7F,OAAO,CAAC,SAAS,GAAG,YAAY,CAAC;QAEjC,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;IAC9C,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,GAAW,EAAE,SAAiB;QAC/D,+CAA+C;QAC/C,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;QAEtD,MAAM,YAAY,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC9C,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAEvB,6CAA6C;QAC7C,MAAM,UAAU,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEjC,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;YACjC,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC;YAE1D,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,SAAS,EAAE,KAAK,EAAE,EAAE,CAAC;gBAC7C,MAAM,QAAQ,GAAG,KAAK,GAAG,SAAS,CAAC;gBACnC,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,SAAS,EAAE,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAE9D,kCAAkC;gBAClC,IAAI,MAAM,GAAG,CAAC,CAAC;gBACf,KAAK,IAAI,GAAG,GAAG,QAAQ,EAAE,GAAG,GAAG,MAAM,EAAE,GAAG,EAAE,EAAE,CAAC;oBAC3C,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;oBACtC,MAAM,QAAQ,GAAG,GAAG,GAAG,CAAC,CAAC;oBACzB,IAAI,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,QAAQ,CAAC,EAAE,CAAC;wBAC5C,MAAM,IAAI,CAAC,CAAC;oBAChB,CAAC;gBACL,CAAC;gBAED,4CAA4C;gBAC5C,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,EAAE,CAAC;oBAC5B,mDAAmD;oBACnD,MAAM,SAAS,GAAG,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC;oBAC7E,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;oBAC5C,MAAM,QAAQ,GAAG,SAAS,GAAG,CAAC,CAAC;oBAC/B,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC;gBAC/C,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,YAAY,CAAC;IACxB,CAAC;IAEO,KAAK,CAAC,2BAA2B,CAAC,OAAoB;QAC1D,OAAO,CAAC,GAAG,CAAC,gDAAgD,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;QAC1E,OAAO,CAAC,MAAM,GAAG,uBAAuB,CAAC;QAEzC,mDAAmD;QACnD,MAAM,kBAAkB,GAAG,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAC/E,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,kBAAkB,CAAC,CAAC;QAEjF,wDAAwD;QACxD,OAAO,CAAC,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;QAE7E,OAAO,CAAC,GAAG,CAAC,mCAAmC,OAAO,CAAC,QAAQ,CAAC,MAAM,kBAAkB,CAAC,CAAC;IAC9F,CAAC;IAEO,2BAA2B,CAAC,SAAiB;QACjD,mEAAmE;QACnE,oEAAoE;QAEpE,IAAI,SAAS,KAAK,CAAC,IAAI,SAAS,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAEjD,MAAM,OAAO,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC;QAC/F,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC,CAAC,CAAC,wBAAwB;IAC5D,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,KAAa,EAAE,YAAoB;QAC3D,8DAA8D;QAC9D,OAAO,CAAC,GAAG,CAAC,4BAA4B,KAAK,CAAC,MAAM,OAAO,YAAY,QAAQ,CAAC,CAAC;QAEjF,0CAA0C;QAC1C,MAAM,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC,UAAU,EAAE,EAAE,YAAY,EAAE,CAAC,CAAC;QAC7D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACnB,IAAI,CAAC,MAAM,CAAC,4BAA4B,CAAC,CAAC;QAC1C,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAEzC,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;IACzB,CAAC;IAEO,iBAAiB;QACrB,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC5D,CAAC;QACD,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,gBAAgB,CAAC,OAAoB;QACzC,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC;QAEpC,4BAA4B;QAC5B,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC;QACxD,IAAI,CAAC,UAAU,CAAC,gBAAgB;YAC5B,CAAC,IAAI,CAAC,UAAU,CAAC,gBAAgB,GAAG,CAAC,aAAa,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC,GAAG,aAAa,CAAC;QAEjG,gCAAgC;QAChC,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YAClB,MAAM,eAAe,GAAG,CAAC,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,CAAC,UAAU;YAChF,MAAM,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;YAC5C,MAAM,IAAI,GAAG,OAAO,GAAG,eAAe,CAAC;YAEvC,IAAI,CAAC,UAAU,CAAC,iBAAiB;gBAC7B,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,GAAG,CAAC,aAAa,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,aAAa,CAAC;QACzF,CAAC;QAED,4BAA4B;QAC5B,MAAM,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC;QACtC,MAAM,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;QAC9C,MAAM,UAAU,GAAG,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAEzD,IAAI,CAAC,UAAU,CAAC,iBAAiB;YAC7B,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,GAAG,CAAC,aAAa,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,GAAG,aAAa,CAAC;IAC/F,CAAC;IAED,UAAU,CAAC,SAAiB;QACxB,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;IACxC,CAAC;IAED,aAAa,CAAC,SAAiB;QAC3B,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC7C,OAAO,OAAO,EAAE,MAAM,KAAK,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC;IACpE,CAAC;IAED,aAAa;QACT,OAAO,EAAE,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;IAClC,CAAC;IAED,gBAAgB;QAQZ,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YACtD,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,YAAY,EAAE,CAAC,GAAG,OAAO,CAAC,YAAY,CAAC;YACvC,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,qBAAqB,EAAE,OAAO,CAAC,qBAAqB;YACpD,iBAAiB,EAAE,OAAO,CAAC,iBAAiB;YAC5C,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC,2BAA2B;SAC/E,CAAC,CAAC,CAAC;IACR,CAAC;IAED,KAAK,CAAC,4BAA4B,CAAC,MAAc,EAAE,YAAoB,GAAG;QACtE,OAAO,CAAC,GAAG,CAAC,yCAAyC,MAAM,EAAE,CAAC,CAAC;QAE/D,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;QAC1E,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QAE3C,IAAI,OAAO,EAAE,MAAM,KAAK,UAAU,EAAE,CAAC;YACjC,OAAO,CAAC,GAAG,CAAC,oCAAoC,OAAO,CAAC,QAAQ,CAAC,MAAM,QAAQ,CAAC,CAAC;YACjF,OAAO,OAAO,CAAC,QAAQ,CAAC;QAC5B,CAAC;aAAM,CAAC;YACJ,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;QACnD,CAAC;IACL,CAAC;IAED,sBAAsB;QAClB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,SAAS;QAEjC,KAAK,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC/C,IAAI,GAAG,GAAG,OAAO,CAAC,SAAS,GAAG,MAAM,EAAE,CAAC;gBACnC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;gBAChC,OAAO,CAAC,GAAG,CAAC,mCAAmC,SAAS,EAAE,CAAC,CAAC;YAChE,CAAC;QACL,CAAC;IACL,CAAC;IAED,OAAO;QACH,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QACtB,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QACtB,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;IAC7D,CAAC;CACJ;AAlaD,wDAkaC"}