"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.FingerprintProtection = void 0;
const electron_1 = require("electron");
const crypto = __importStar(require("crypto"));
class FingerprintProtection {
    constructor() {
        this.rotationInterval = null;
        this.profiles = [];
        this.currentProfile = this.generateRandomProfile();
    }
    async initialize() {
        this.generateProfiles();
        this.setupFingerprintProtection();
        this.startProfileRotation();
    }
    generateProfiles() {
        // Generate multiple realistic fingerprint profiles
        const profiles = [
            this.createWindowsProfile(),
            this.createMacProfile(),
            this.createLinuxProfile(),
            this.createMobileProfile()
        ];
        this.profiles = profiles;
        this.currentProfile = profiles[Math.floor(Math.random() * profiles.length)];
    }
    createWindowsProfile() {
        return {
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            screen: {
                width: 1920,
                height: 1080,
                colorDepth: 24,
                pixelDepth: 24
            },
            timezone: 'America/New_York',
            language: 'en-US',
            platform: 'Win32',
            hardwareConcurrency: 8,
            deviceMemory: 8,
            webglVendor: 'Google Inc. (NVIDIA)',
            webglRenderer: 'ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0, D3D11)',
            canvasFingerprint: this.generateCanvasFingerprint(),
            audioFingerprint: this.generateAudioFingerprint()
        };
    }
    createMacProfile() {
        return {
            userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            screen: {
                width: 2560,
                height: 1440,
                colorDepth: 24,
                pixelDepth: 24
            },
            timezone: 'America/Los_Angeles',
            language: 'en-US',
            platform: 'MacIntel',
            hardwareConcurrency: 10,
            deviceMemory: 16,
            webglVendor: 'Apple Inc.',
            webglRenderer: 'Apple M1 Pro',
            canvasFingerprint: this.generateCanvasFingerprint(),
            audioFingerprint: this.generateAudioFingerprint()
        };
    }
    createLinuxProfile() {
        return {
            userAgent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            screen: {
                width: 1920,
                height: 1080,
                colorDepth: 24,
                pixelDepth: 24
            },
            timezone: 'Europe/London',
            language: 'en-GB',
            platform: 'Linux x86_64',
            hardwareConcurrency: 12,
            deviceMemory: 32,
            webglVendor: 'Mesa',
            webglRenderer: 'Mesa Intel(R) UHD Graphics 630 (CFL GT2)',
            canvasFingerprint: this.generateCanvasFingerprint(),
            audioFingerprint: this.generateAudioFingerprint()
        };
    }
    createMobileProfile() {
        return {
            userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1',
            screen: {
                width: 390,
                height: 844,
                colorDepth: 24,
                pixelDepth: 24
            },
            timezone: 'America/New_York',
            language: 'en-US',
            platform: 'iPhone',
            hardwareConcurrency: 6,
            deviceMemory: 6,
            webglVendor: 'Apple Inc.',
            webglRenderer: 'Apple A16 GPU',
            canvasFingerprint: this.generateCanvasFingerprint(),
            audioFingerprint: this.generateAudioFingerprint()
        };
    }
    generateCanvasFingerprint() {
        return crypto.randomBytes(16).toString('hex');
    }
    generateAudioFingerprint() {
        return crypto.randomBytes(12).toString('hex');
    }
    generateRandomProfile() {
        const baseProfiles = [
            this.createWindowsProfile(),
            this.createMacProfile(),
            this.createLinuxProfile()
        ];
        const base = baseProfiles[Math.floor(Math.random() * baseProfiles.length)];
        // Add some randomization
        base.screen.width += Math.floor(Math.random() * 200) - 100;
        base.screen.height += Math.floor(Math.random() * 100) - 50;
        base.hardwareConcurrency = Math.floor(Math.random() * 8) + 4;
        base.deviceMemory = [4, 8, 16, 32][Math.floor(Math.random() * 4)];
        return base;
    }
    setupFingerprintProtection() {
        const ses = electron_1.session.defaultSession;
        // Inject fingerprint protection script into all pages
        ses.webRequest.onHeadersReceived({ urls: ['<all_urls>'] }, (details, callback) => {
            if (details.responseHeaders &&
                details.responseHeaders['content-type']?.[0]?.includes('text/html')) {
                // Inject comprehensive fingerprint protection
                const protectionScript = this.generateProtectionScript();
                details.responseHeaders['X-Fingerprint-Protection'] = [protectionScript];
            }
            callback({ responseHeaders: details.responseHeaders });
        });
        // Override user agent
        ses.setUserAgent(this.currentProfile.userAgent);
    }
    generateProtectionScript() {
        const profile = this.currentProfile;
        return `
            <script>
            (function() {
                'use strict';
                
                // Screen fingerprinting protection
                Object.defineProperties(screen, {
                    width: { value: ${profile.screen.width}, configurable: false },
                    height: { value: ${profile.screen.height}, configurable: false },
                    availWidth: { value: ${profile.screen.width}, configurable: false },
                    availHeight: { value: ${profile.screen.height - 40}, configurable: false },
                    colorDepth: { value: ${profile.screen.colorDepth}, configurable: false },
                    pixelDepth: { value: ${profile.screen.pixelDepth}, configurable: false }
                });

                // Navigator fingerprinting protection
                Object.defineProperties(navigator, {
                    platform: { value: '${profile.platform}', configurable: false },
                    language: { value: '${profile.language}', configurable: false },
                    languages: { value: ['${profile.language}'], configurable: false },
                    hardwareConcurrency: { value: ${profile.hardwareConcurrency}, configurable: false },
                    deviceMemory: { value: ${profile.deviceMemory}, configurable: false },
                    userAgent: { value: '${profile.userAgent}', configurable: false }
                });

                // Canvas fingerprinting protection
                const originalGetContext = HTMLCanvasElement.prototype.getContext;
                HTMLCanvasElement.prototype.getContext = function(type, ...args) {
                    const context = originalGetContext.apply(this, [type, ...args]);
                    
                    if (type === '2d') {
                        const originalGetImageData = context.getImageData;
                        context.getImageData = function(...args) {
                            const imageData = originalGetImageData.apply(this, args);
                            // Add subtle noise to prevent fingerprinting
                            for (let i = 0; i < imageData.data.length; i += 4) {
                                imageData.data[i] += Math.floor(Math.random() * 3) - 1;
                                imageData.data[i + 1] += Math.floor(Math.random() * 3) - 1;
                                imageData.data[i + 2] += Math.floor(Math.random() * 3) - 1;
                            }
                            return imageData;
                        };
                    }
                    
                    return context;
                };

                // WebGL fingerprinting protection
                const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
                WebGLRenderingContext.prototype.getParameter = function(parameter) {
                    if (parameter === this.VENDOR) {
                        return '${profile.webglVendor}';
                    }
                    if (parameter === this.RENDERER) {
                        return '${profile.webglRenderer}';
                    }
                    return originalGetParameter.apply(this, arguments);
                };

                // Audio fingerprinting protection
                const originalCreateAnalyser = AudioContext.prototype.createAnalyser;
                AudioContext.prototype.createAnalyser = function() {
                    const analyser = originalCreateAnalyser.apply(this, arguments);
                    const originalGetFloatFrequencyData = analyser.getFloatFrequencyData;
                    analyser.getFloatFrequencyData = function(array) {
                        originalGetFloatFrequencyData.apply(this, arguments);
                        // Add noise to audio fingerprinting
                        for (let i = 0; i < array.length; i++) {
                            array[i] += Math.random() * 0.001 - 0.0005;
                        }
                    };
                    return analyser;
                };

                // Font fingerprinting protection
                const originalOffsetWidth = Object.getOwnPropertyDescriptor(HTMLElement.prototype, 'offsetWidth');
                const originalOffsetHeight = Object.getOwnPropertyDescriptor(HTMLElement.prototype, 'offsetHeight');
                
                Object.defineProperty(HTMLElement.prototype, 'offsetWidth', {
                    get: function() {
                        const width = originalOffsetWidth.get.call(this);
                        return width + (Math.random() * 0.1 - 0.05);
                    }
                });

                Object.defineProperty(HTMLElement.prototype, 'offsetHeight', {
                    get: function() {
                        const height = originalOffsetHeight.get.call(this);
                        return height + (Math.random() * 0.1 - 0.05);
                    }
                });

                // Timezone spoofing
                const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;
                Date.prototype.getTimezoneOffset = function() {
                    // Return offset for ${profile.timezone}
                    return ${this.getTimezoneOffset(profile.timezone)};
                };

                // WebRTC leak protection
                if (window.RTCPeerConnection) {
                    const originalCreateDataChannel = RTCPeerConnection.prototype.createDataChannel;
                    RTCPeerConnection.prototype.createDataChannel = function() {
                        throw new Error('WebRTC data channels disabled for privacy');
                    };
                }

                // Battery API blocking
                if (navigator.getBattery) {
                    navigator.getBattery = function() {
                        return Promise.reject(new Error('Battery API disabled for privacy'));
                    };
                }

                // Gamepad API blocking
                Object.defineProperty(navigator, 'getGamepads', {
                    value: function() { return []; }
                });

                console.log('Fingerprint protection active');
            })();
            </script>
        `;
    }
    getTimezoneOffset(timezone) {
        // Simplified timezone offset calculation
        const offsets = {
            'America/New_York': 300,
            'America/Los_Angeles': 480,
            'Europe/London': 0,
            'Asia/Tokyo': -540
        };
        return offsets[timezone] || 0;
    }
    startProfileRotation() {
        // Rotate fingerprint profile every 30 minutes
        this.rotationInterval = setInterval(() => {
            this.rotateProfile();
        }, 30 * 60 * 1000);
    }
    rotateProfile() {
        const newProfile = this.profiles[Math.floor(Math.random() * this.profiles.length)];
        this.currentProfile = newProfile;
        // Update session user agent
        electron_1.session.defaultSession.setUserAgent(newProfile.userAgent);
        console.log('Fingerprint profile rotated');
    }
    getCurrentProfile() {
        return { ...this.currentProfile };
    }
    setProfile(profile) {
        this.currentProfile = profile;
        electron_1.session.defaultSession.setUserAgent(profile.userAgent);
    }
    destroy() {
        if (this.rotationInterval) {
            clearInterval(this.rotationInterval);
            this.rotationInterval = null;
        }
    }
}
exports.FingerprintProtection = FingerprintProtection;
//# sourceMappingURL=FingerprintProtection.js.map