{"version": 3, "file": "SteganographicManager.js", "sourceRoot": "", "sources": ["../../src/steganography/SteganographicManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,uCAAkD;AAClD,+CAAiC;AAoBjC,MAAa,qBAAqB;IAS9B;QAPQ,yBAAoB,GAA0B,IAAI,CAAC;QACnD,4BAAuB,GAA0B,IAAI,CAAC;QACtD,iBAAY,GAA2E,EAAE,CAAC;QAC1F,mBAAc,GAAwB,IAAI,GAAG,EAAE,CAAC;QAChD,oBAAe,GAAqB,EAAE,CAAC;QACvC,wBAAmB,GAAW,CAAC,CAAC;QAGpC,IAAI,CAAC,QAAQ,GAAG;YACZ,wBAAwB,EAAE,IAAI;YAC9B,yBAAyB,EAAE,IAAI;YAC/B,qBAAqB,EAAE,IAAI;YAC3B,kBAAkB,EAAE,IAAI;YACxB,oBAAoB,EAAE,IAAI;YAC1B,uBAAuB,EAAE,IAAI;YAC7B,oBAAoB,EAAE,IAAI;YAC1B,oBAAoB,EAAE,MAAM;SAC/B,CAAC;QAEF,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACrC,CAAC;IAED,KAAK,CAAC,UAAU;QACZ,MAAM,IAAI,CAAC,+BAA+B,EAAE,CAAC;QAC7C,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;QACtC,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAClC,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC/B,gEAAgE;QAChE,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACjC,IAAI,CAAC,6BAA6B,EAAE,CAAC;IACzC,CAAC;IAEO,yBAAyB;QAC7B,6CAA6C;QAC7C,IAAI,CAAC,eAAe,GAAG;YACnB,0BAA0B;YAC1B,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,EAAE;YAC7E,kDAAkD;YAClD,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,EAAE;YAC9E,iDAAiD;YACjD,EAAE,eAAe,EAAE,GAAG,EAAE,SAAS,EAAE,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,EAAE;YAC5E,+CAA+C;YAC/C,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,EAAE;YAC7E,uBAAuB;YACvB,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,EAAE,aAAa,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,EAAE;SACjF,CAAC;IACN,CAAC;IAEO,KAAK,CAAC,+BAA+B;QACzC,MAAM,GAAG,GAAG,kBAAO,CAAC,cAAc,CAAC;QAEnC,sCAAsC;QACtC,GAAG,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE,IAAI,EAAE,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE;YAC3E,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,wBAAwB,EAAE,CAAC;gBAC1C,QAAQ,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;gBAC5B,OAAO;YACX,CAAC;YAED,MAAM,KAAK,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YACzD,MAAM,gBAAgB,GAAG,IAAI,CAAC,QAAQ,CAAC,oBAAoB,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC;YAEnF,IAAI,gBAAgB,EAAE,CAAC;gBACnB,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YACpC,CAAC;YAED,UAAU,CAAC,GAAG,EAAE;gBACZ,QAAQ,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;YAChC,CAAC,EAAE,KAAK,CAAC,CAAC;QACd,CAAC,CAAC,CAAC;QAEH,8BAA8B;QAC9B,GAAG,CAAC,UAAU,CAAC,mBAAmB,CAAC,EAAE,IAAI,EAAE,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE;YAC/E,MAAM,OAAO,GAAG,OAAO,CAAC,cAAc,CAAC;YAEvC,IAAI,IAAI,CAAC,QAAQ,CAAC,wBAAwB,EAAE,CAAC;gBACzC,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;YACvD,CAAC;YAED,QAAQ,CAAC,EAAE,cAAc,EAAE,OAAO,EAAE,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,wBAAwB,CAAC,GAAW;QACxC,MAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;QAC/C,MAAM,OAAO,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAChD,MAAM,YAAY,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC,UAAU,CAAC;QAEhE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,GAAG,CAAC,SAAS,GAAG,YAAY,CAAC,CAAC,CAAC;IAC/D,CAAC;IAEO,kBAAkB,CAAC,GAAW;QAClC,oDAAoD;QACpD,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YAC9C,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,sBAAsB;QAC3D,CAAC;aAAM,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAC9E,OAAO,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,uBAAuB;QAC7D,CAAC;aAAM,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACtD,OAAO,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,0BAA0B;QAChE,CAAC;aAAM,CAAC;YACJ,OAAO,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,8BAA8B;QACpE,CAAC;IACL,CAAC;IAEO,wBAAwB;QAC5B,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;IAC1D,CAAC;IAEO,uBAAuB,CAAC,OAA+B,EAAE,GAAW;QACxE,mCAAmC;QACnC,MAAM,eAAe,GAAG;YACpB,gBAAgB;YAChB,yBAAyB;YACzB,4BAA4B;YAC5B,yBAAyB;SAC5B,CAAC;QAEF,MAAM,eAAe,GAAG;YACpB,mBAAmB;YACnB,eAAe;YACf,yBAAyB;SAC5B,CAAC;QAEF,yBAAyB;QACzB,OAAO,CAAC,iBAAiB,CAAC,GAAG,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC;QACjG,OAAO,CAAC,iBAAiB,CAAC,GAAG,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC;QAEjG,sCAAsC;QACtC,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,CAAC;YACtB,OAAO,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC;QAC9E,CAAC;QAED,oCAAoC;QACpC,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,CAAC;YACtB,OAAO,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC;QACzE,CAAC;QAED,0CAA0C;QAC1C,IAAI,GAAG,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,CAAC;YACpD,OAAO,CAAC,2BAA2B,CAAC,GAAG,GAAG,CAAC;QAC/C,CAAC;QAED,2CAA2C;QAC3C,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,CAAC;YACtB,OAAO,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC;YAC5D,OAAO,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;YACzD,OAAO,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7D,CAAC;IACL,CAAC;IAEO,qBAAqB,CAAC,GAAW;QACrC,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC;YAAE,OAAO,OAAO,CAAC;QACzC,IAAI,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC;YAAE,OAAO,QAAQ,CAAC;QACzC,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC;YAAE,OAAO,OAAO,CAAC;QACjE,OAAO,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC;IACtD,CAAC;IAEO,qBAAqB;QACzB,MAAM,KAAK,GAAG,CAAC,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;QAC7D,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IAC3D,CAAC;IAEO,qBAAqB;QACzB,MAAM,KAAK,GAAG,CAAC,aAAa,EAAE,YAAY,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC;QACjE,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IAC3D,CAAC;IAEO,KAAK,CAAC,wBAAwB;QAClC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,yBAAyB;YAAE,OAAO;QAErD,+CAA+C;QAC/C,WAAW,CAAC,GAAG,EAAE;YACb,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAChC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IAC7C,CAAC;IAEO,oBAAoB;QACxB,IAAI,CAAC,mBAAmB,GAAG,CAAC,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;QACxF,OAAO,CAAC,GAAG,CAAC,+BAA+B,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC;IAC3E,CAAC;IAEO,KAAK,CAAC,oBAAoB;QAC9B,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,qBAAqB;YAAE,OAAO;QAEjD,uCAAuC;QACvC,IAAI,CAAC,uBAAuB,GAAG,WAAW,CAAC,GAAG,EAAE;YAC5C,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACjC,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,sBAAsB;IAC7D,CAAC;IAEO,qBAAqB;QACzB,MAAM,SAAS,GAAG;YACd,GAAG,EAAE,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAC9B,GAAG,EAAE,CAAC,IAAI,CAAC,qBAAqB,EAAE;YAClC,GAAG,EAAE,CAAC,IAAI,CAAC,cAAc,EAAE;YAC3B,GAAG,EAAE,CAAC,IAAI,CAAC,oBAAoB,EAAE;YACjC,GAAG,EAAE,CAAC,IAAI,CAAC,iBAAiB,EAAE;SACjC,CAAC;QAEF,MAAM,cAAc,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;QAC/E,cAAc,EAAE,CAAC;IACrB,CAAC;IAEO,iBAAiB;QACrB,oDAAoD;QACpD,MAAM,OAAO,GAAG,wBAAa,CAAC,aAAa,EAAE,CAAC;QAC9C,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrB,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1B,MAAM,CAAC,WAAW,CAAC,iBAAiB,CAAC;qCACZ,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,EAAE;aAChD,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE;gBACV,kDAAkD;YACtD,CAAC,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAEO,qBAAqB;QACzB,8CAA8C;QAC9C,sDAAsD;QACtD,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;IAClE,CAAC;IAEO,cAAc;QAClB,8CAA8C;QAC9C,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;IAC1D,CAAC;IAEO,oBAAoB;QACxB,kCAAkC;QAClC,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;IACjE,CAAC;IAEO,iBAAiB;QACrB,iCAAiC;QACjC,MAAM,aAAa,GAAG,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC,CAAC,eAAe;QACnE,OAAO,CAAC,GAAG,CAAC,6BAA6B,aAAa,IAAI,CAAC,CAAC;IAChE,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC3B,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,kBAAkB;YAAE,OAAO;QAE9C,+CAA+C;QAC/C,IAAI,CAAC,oBAAoB,GAAG,WAAW,CAAC,GAAG,EAAE;YACzC,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAChC,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,oBAAoB;IAC5D,CAAC;IAEO,oBAAoB;QACxB,MAAM,YAAY,GAAG;YACjB,aAAa;YACb,aAAa;YACb,8BAA8B;YAC9B,WAAW;YACX,aAAa;SAChB,CAAC;QAEF,MAAM,YAAY,GAAG,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;QACnF,MAAM,QAAQ,GAAG,WAAW,YAAY,EAAE,CAAC;QAE3C,6DAA6D;QAC7D,KAAK,CAAC,QAAQ,EAAE;YACZ,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,SAAS;SAClB,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE;YACV,6CAA6C;QACjD,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,+BAA+B,QAAQ,EAAE,CAAC,CAAC;IAC3D,CAAC;IAEO,iBAAiB,CAAC,OAAY;QAClC,qDAAqD;QACrD,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,oBAAoB;QAC1E,MAAM,OAAO,GAAG,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAEnE,mEAAmE;QACnE,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;YACzB,OAAO,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,oBAAoB;QACzF,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,mBAAmB;QAC7B,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,oBAAoB;YAAE,OAAO;QAEhD,kCAAkC;QAClC,MAAM,GAAG,GAAG,kBAAO,CAAC,cAAc,CAAC;QAEnC,iDAAiD;QACjD,MAAM,YAAY,GAAG;YACjB,2BAA2B;YAC3B,2BAA2B;YAC3B,2BAA2B;YAC3B,mCAAmC;SACtC,CAAC;QAEF,oCAAoC;QACpC,WAAW,CAAC,GAAG,EAAE;YACb,MAAM,cAAc,GAAG,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;YACrF,OAAO,CAAC,GAAG,CAAC,6BAA6B,cAAc,EAAE,CAAC,CAAC;QAC/D,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,kBAAkB;IAClC,CAAC;IAEO,6BAA6B;QACjC,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QAEjD,uDAAuD;QACvD,QAAQ,IAAI,CAAC,QAAQ,CAAC,oBAAoB,EAAE,CAAC;YACzC,KAAK,SAAS;gBACV,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBAC/B,MAAM;YACV,KAAK,MAAM;gBACP,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAC5B,MAAM;YACV,KAAK,QAAQ;gBACT,IAAI,CAAC,sBAAsB,EAAE,CAAC;gBAC9B,MAAM;YACV,KAAK,KAAK;gBACN,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC3B,MAAM;QACd,CAAC;IACL,CAAC;IAEO,uBAAuB;QAC3B,6EAA6E;QAC7E,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,WAAW,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC,mBAAmB;IAC9E,CAAC;IAEO,oBAAoB;QACxB,+DAA+D;QAC/D,WAAW,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC,eAAe;IAC1E,CAAC;IAEO,sBAAsB;QAC1B,gEAAgE;QAChE,WAAW,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,kBAAkB;IAC9E,CAAC;IAEO,mBAAmB;QACvB,kDAAkD;QAClD,WAAW,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,kBAAkB;IAC9E,CAAC;IAED,cAAc,CAAC,WAA4C;QACvD,IAAI,CAAC,QAAQ,GAAG,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,WAAW,EAAE,CAAC;QACrD,OAAO,CAAC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IACnE,CAAC;IAED,WAAW;QACP,OAAO,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;IAChC,CAAC;IAED,OAAO;QACH,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,aAAa,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACzC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;QACrC,CAAC;QAED,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC/B,aAAa,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YAC5C,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;QACxC,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;IACpD,CAAC;CACJ;AA9WD,sDA8WC"}