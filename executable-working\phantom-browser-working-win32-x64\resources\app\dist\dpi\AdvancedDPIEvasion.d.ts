export interface DPIEvasionConfig {
    enableProtocolObfuscation: boolean;
    enableTrafficShaping: boolean;
    enableMultiLayerTunneling: boolean;
    enableApplicationMimicry: boolean;
    obfuscationIntensity: number;
    targetApplications: string[];
}
export interface ProtocolObfuscation {
    method: 'header_randomization' | 'payload_encryption' | 'protocol_tunneling' | 'steganographic_embedding';
    strength: number;
    overhead: number;
    detectability: number;
}
export interface TrafficShapingProfile {
    name: string;
    application: string;
    patterns: {
        packetSizes: number[];
        timingIntervals: number[];
        burstCharacteristics: {
            size: number;
            frequency: number;
            duration: number;
        };
        flowDirection: 'bidirectional' | 'upload_heavy' | 'download_heavy';
    };
    fingerprint: string;
}
export declare class AdvancedDPIEvasion {
    private config;
    private obfuscationMethods;
    private shapingProfiles;
    private activeObfuscation;
    private tunnelLayers;
    private mimicryTarget;
    constructor();
    initialize(): Promise<void>;
    private initializeObfuscationMethods;
    private initializeShapingProfiles;
    private generateFingerprint;
    private setupProtocolObfuscation;
    private shouldApplyObfuscation;
    private randomizeHeaders;
    private generateRandomHeaderValue;
    private obfuscateUserAgent;
    private generateChromeVersion;
    private generateFirefoxVersion;
    private randomizeHeaderCase;
    private obfuscatePayload;
    private setupTrafficShaping;
    private applyTrafficShaping;
    private calculateShapingDelay;
    private generateBackgroundTraffic;
    private sendDummyRequest;
    private setupMultiLayerTunneling;
    private setupTunnelLayer;
    private setupHTTPTunneling;
    private setupWebSocketTunneling;
    private setupDNSTunneling;
    private setupSteganographicTunneling;
    private setupProtocolHopping;
    private setupApplicationMimicry;
    private mimicApplicationBehavior;
    private mimicVideoStreaming;
    private mimicFileDownload;
    private mimicMessaging;
    private mimicHTTPSBrowsing;
    updateConfiguration(newConfig: Partial<DPIEvasionConfig>): void;
    getEvasionStatus(): {
        activeObfuscation: string[];
        tunnelLayers: number;
        mimicryTarget: string;
        intensity: number;
    };
    rotateObfuscationMethods(): void;
    rotateMimicryTarget(): void;
    private startObfuscationRotation;
}
//# sourceMappingURL=AdvancedDPIEvasion.d.ts.map