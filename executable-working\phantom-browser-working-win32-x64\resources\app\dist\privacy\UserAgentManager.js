"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserAgentManager = void 0;
const electron_1 = require("electron");
class UserAgentManager {
    constructor() {
        this.rotationEnabled = true;
        this.rotationInterval = null;
        this.profiles = [];
        this.currentProfile = this.generateRandomProfile();
    }
    async initialize() {
        this.generateUserAgentProfiles();
        this.setupUserAgentRotation();
        this.applyCurrentProfile();
    }
    generateUserAgentProfiles() {
        this.profiles = [
            // Chrome on Windows
            {
                userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                platform: 'Win32',
                browser: 'Chrome',
                version: '120.0.0.0',
                os: 'Windows 10',
                device: 'Desktop',
                acceptLanguage: 'en-US,en;q=0.9',
                acceptEncoding: 'gzip, deflate, br',
                acceptCharset: 'ISO-8859-1,utf-8;q=0.7,*;q=0.3'
            },
            // Chrome on macOS
            {
                userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                platform: 'MacIntel',
                browser: 'Chrome',
                version: '120.0.0.0',
                os: 'macOS 10.15.7',
                device: 'Desktop',
                acceptLanguage: 'en-US,en;q=0.9',
                acceptEncoding: 'gzip, deflate, br',
                acceptCharset: 'ISO-8859-1,utf-8;q=0.7,*;q=0.3'
            },
            // Firefox on Windows
            {
                userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
                platform: 'Win32',
                browser: 'Firefox',
                version: '121.0',
                os: 'Windows 10',
                device: 'Desktop',
                acceptLanguage: 'en-US,en;q=0.5',
                acceptEncoding: 'gzip, deflate, br',
                acceptCharset: 'ISO-8859-1,utf-8;q=0.7,*;q=0.7'
            },
            // Safari on macOS
            {
                userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
                platform: 'MacIntel',
                browser: 'Safari',
                version: '17.1',
                os: 'macOS 10.15.7',
                device: 'Desktop',
                acceptLanguage: 'en-US,en;q=0.9',
                acceptEncoding: 'gzip, deflate, br',
                acceptCharset: 'ISO-8859-1,utf-8;q=0.7,*;q=0.3'
            },
            // Chrome on Linux
            {
                userAgent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                platform: 'Linux x86_64',
                browser: 'Chrome',
                version: '120.0.0.0',
                os: 'Linux',
                device: 'Desktop',
                acceptLanguage: 'en-US,en;q=0.9',
                acceptEncoding: 'gzip, deflate, br',
                acceptCharset: 'ISO-8859-1,utf-8;q=0.7,*;q=0.3'
            },
            // Edge on Windows
            {
                userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
                platform: 'Win32',
                browser: 'Edge',
                version: '120.0.0.0',
                os: 'Windows 10',
                device: 'Desktop',
                acceptLanguage: 'en-US,en;q=0.9',
                acceptEncoding: 'gzip, deflate, br',
                acceptCharset: 'ISO-8859-1,utf-8;q=0.7,*;q=0.3'
            }
        ];
        // Add some mobile profiles
        this.profiles.push({
            userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1',
            platform: 'iPhone',
            browser: 'Safari',
            version: '17.1',
            os: 'iOS 17.1',
            device: 'Mobile',
            acceptLanguage: 'en-US,en;q=0.9',
            acceptEncoding: 'gzip, deflate, br',
            acceptCharset: 'ISO-8859-1,utf-8;q=0.7,*;q=0.3'
        }, {
            userAgent: 'Mozilla/5.0 (Linux; Android 14; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
            platform: 'Linux armv8l',
            browser: 'Chrome',
            version: '120.0.0.0',
            os: 'Android 14',
            device: 'Mobile',
            acceptLanguage: 'en-US,en;q=0.9',
            acceptEncoding: 'gzip, deflate, br',
            acceptCharset: 'ISO-8859-1,utf-8;q=0.7,*;q=0.3'
        });
        // Select random initial profile
        this.currentProfile = this.profiles[Math.floor(Math.random() * this.profiles.length)];
    }
    generateRandomProfile() {
        const browsers = ['Chrome', 'Firefox', 'Safari', 'Edge'];
        const platforms = ['Win32', 'MacIntel', 'Linux x86_64'];
        const versions = ['120.0.0.0', '*********', '*********'];
        const browser = browsers[Math.floor(Math.random() * browsers.length)];
        const platform = platforms[Math.floor(Math.random() * platforms.length)];
        const version = versions[Math.floor(Math.random() * versions.length)];
        let userAgent = '';
        let os = '';
        switch (platform) {
            case 'Win32':
                os = 'Windows 10';
                userAgent = `Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) ${browser}/${version} Safari/537.36`;
                break;
            case 'MacIntel':
                os = 'macOS 10.15.7';
                if (browser === 'Safari') {
                    userAgent = `Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15`;
                }
                else {
                    userAgent = `Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) ${browser}/${version} Safari/537.36`;
                }
                break;
            case 'Linux x86_64':
                os = 'Linux';
                userAgent = `Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) ${browser}/${version} Safari/537.36`;
                break;
        }
        return {
            userAgent,
            platform,
            browser,
            version,
            os,
            device: 'Desktop',
            acceptLanguage: 'en-US,en;q=0.9',
            acceptEncoding: 'gzip, deflate, br',
            acceptCharset: 'ISO-8859-1,utf-8;q=0.7,*;q=0.3'
        };
    }
    setupUserAgentRotation() {
        if (this.rotationEnabled) {
            // Rotate user agent every 15 minutes
            this.rotationInterval = setInterval(() => {
                this.rotateUserAgent();
            }, 15 * 60 * 1000);
        }
    }
    applyCurrentProfile() {
        const ses = electron_1.session.defaultSession;
        // Set user agent
        ses.setUserAgent(this.currentProfile.userAgent);
        // Modify request headers to match profile
        ses.webRequest.onBeforeSendHeaders({ urls: ['<all_urls>'] }, (details, callback) => {
            const headers = details.requestHeaders;
            // Set consistent headers based on profile
            headers['Accept-Language'] = this.currentProfile.acceptLanguage;
            headers['Accept-Encoding'] = this.currentProfile.acceptEncoding;
            headers['Accept'] = this.getAcceptHeader(details.url);
            // Add browser-specific headers
            if (this.currentProfile.browser === 'Chrome') {
                headers['sec-ch-ua'] = this.generateSecChUa();
                headers['sec-ch-ua-mobile'] = this.currentProfile.device === 'Mobile' ? '?1' : '?0';
                headers['sec-ch-ua-platform'] = `"${this.getPlatformName()}"`;
            }
            // Remove identifying headers
            delete headers['X-Requested-With'];
            delete headers['X-Forwarded-For'];
            callback({ requestHeaders: headers });
        });
    }
    getAcceptHeader(url) {
        if (url.includes('.css')) {
            return 'text/css,*/*;q=0.1';
        }
        else if (url.includes('.js')) {
            return '*/*';
        }
        else if (url.includes('.png') || url.includes('.jpg') || url.includes('.gif')) {
            return 'image/webp,image/apng,image/*,*/*;q=0.8';
        }
        else {
            return 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8';
        }
    }
    generateSecChUa() {
        const brands = [
            '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            '"Not_A Brand";v="8", "Chromium";v="121", "Google Chrome";v="121"',
            '"Chromium";v="120", "Not(A:Brand";v="24", "Google Chrome";v="120"'
        ];
        return brands[Math.floor(Math.random() * brands.length)];
    }
    getPlatformName() {
        switch (this.currentProfile.platform) {
            case 'Win32': return 'Windows';
            case 'MacIntel': return 'macOS';
            case 'Linux x86_64': return 'Linux';
            default: return 'Unknown';
        }
    }
    rotateUserAgent() {
        // Select a different profile
        const availableProfiles = this.profiles.filter(p => p !== this.currentProfile);
        this.currentProfile = availableProfiles[Math.floor(Math.random() * availableProfiles.length)];
        this.applyCurrentProfile();
        console.log(`User agent rotated to: ${this.currentProfile.browser} on ${this.currentProfile.os}`);
    }
    setProfile(profile) {
        this.currentProfile = profile;
        this.applyCurrentProfile();
    }
    getCurrentProfile() {
        return { ...this.currentProfile };
    }
    enableRotation() {
        this.rotationEnabled = true;
        this.setupUserAgentRotation();
    }
    disableRotation() {
        this.rotationEnabled = false;
        if (this.rotationInterval) {
            clearInterval(this.rotationInterval);
            this.rotationInterval = null;
        }
    }
    getAvailableProfiles() {
        return [...this.profiles];
    }
    addCustomProfile(profile) {
        this.profiles.push(profile);
    }
    destroy() {
        if (this.rotationInterval) {
            clearInterval(this.rotationInterval);
            this.rotationInterval = null;
        }
    }
}
exports.UserAgentManager = UserAgentManager;
//# sourceMappingURL=UserAgentManager.js.map