export interface SteganographicSettings {
    enableTrafficObfuscation: boolean;
    enableTimingRandomization: boolean;
    enableBehaviorMasking: boolean;
    enableDecoyTraffic: boolean;
    enableRequestPadding: boolean;
    enableConnectionPooling: boolean;
    enableDNSObfuscation: boolean;
    obfuscationIntensity: 'low' | 'medium' | 'high' | 'maximum';
}
export interface TrafficPattern {
    requestInterval: number;
    burstSize: number;
    pauseDuration: number;
    randomness: number;
}
export declare class SteganographicManager {
    private settings;
    private decoyTrafficInterval;
    private behaviorMaskingInterval;
    private requestQueue;
    private connectionPool;
    private trafficPatterns;
    private currentPatternIndex;
    constructor();
    initialize(): Promise<void>;
    private initializeTrafficPatterns;
    private setupAdvancedTrafficObfuscation;
    private calculateObfuscatedDelay;
    private getBaseDelayForURL;
    private getCurrentTrafficPattern;
    private obfuscateRequestHeaders;
    private getRandomSecFetchDest;
    private getRandomSecFetchMode;
    private getRandomSecFetchSite;
    private setupTimingRandomization;
    private rotateTrafficPattern;
    private setupBehaviorMasking;
    private simulateHumanBehavior;
    private simulateScrolling;
    private simulateMouseMovement;
    private simulateTyping;
    private simulateTabSwitching;
    private simulateIdlePause;
    private setupDecoyTraffic;
    private generateDecoyTraffic;
    private addRequestPadding;
    private setupDNSObfuscation;
    private startSteganographicOperations;
    private startMaximumObfuscation;
    private startHighObfuscation;
    private startMediumObfuscation;
    private startLowObfuscation;
    updateSettings(newSettings: Partial<SteganographicSettings>): void;
    getSettings(): SteganographicSettings;
    destroy(): void;
}
//# sourceMappingURL=SteganographicManager.d.ts.map