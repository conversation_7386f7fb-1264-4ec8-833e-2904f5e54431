{"version": 3, "file": "SearchUI.js", "sourceRoot": "", "sources": ["../../src/search/SearchUI.ts"], "names": [], "mappings": ";;;AAEA,MAAa,QAAQ;IAOjB,YAAY,YAA0B;QAL9B,wBAAmB,GAAuB,IAAI,CAAC;QAC/C,2BAAsB,GAAG,CAAC,CAAC,CAAC;QAC5B,gBAAW,GAAuB,EAAE,CAAC;QACrC,kBAAa,GAA0B,IAAI,CAAC;QAGhD,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,YAAY,EAAE,CAAC;IACxB,CAAC;IAEO,YAAY;QAChB,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACjC,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,IAAI,CAAC,4BAA4B,EAAE,CAAC;IACxC,CAAC;IAEO,yBAAyB;QAC7B,8BAA8B;QAC9B,IAAI,CAAC,mBAAmB,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QACzD,IAAI,CAAC,mBAAmB,CAAC,EAAE,GAAG,mBAAmB,CAAC;QAClD,IAAI,CAAC,mBAAmB,CAAC,SAAS,GAAG,2BAA2B,CAAC;QACjE,IAAI,CAAC,mBAAmB,CAAC,SAAS,GAAG;;SAEpC,CAAC;QAEF,2BAA2B;QAC3B,MAAM,UAAU,GAAG,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;QACzD,IAAI,UAAU,IAAI,UAAU,CAAC,UAAU,EAAE,CAAC;YACtC,UAAU,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,mBAAmB,EAAE,UAAU,CAAC,WAAW,CAAC,CAAC;QACzF,CAAC;IACL,CAAC;IAEO,4BAA4B;QAChC,mDAAmD;QACnD,MAAM,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;QAC9D,IAAI,CAAC,YAAY;YAAE,OAAO;QAE1B,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QACpD,aAAa,CAAC,SAAS,GAAG,iBAAiB,CAAC;QAC5C,aAAa,CAAC,SAAS,GAAG;;;;;sBAKZ,IAAI,CAAC,uBAAuB,EAAE;;;;;;;;;;;;;;;;;;;;SAoB3C,CAAC;QAEF,YAAY,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;QACxC,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC9B,CAAC;IAEO,uBAAuB;QAC3B,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC;QACnD,MAAM,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC;QAE/D,OAAO,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAC5B,kBAAkB,QAAQ,CAAC,EAAE,KAAK,QAAQ,CAAC,EAAE,KAAK,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;kBAChF,QAAQ,CAAC,IAAI,cAAc,QAAQ,CAAC,aAAa;sBAC7C,CACb,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACf,CAAC;IAEO,mBAAmB;QACvB,MAAM,UAAU,GAAG,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAqB,CAAC;QAC7E,IAAI,CAAC,UAAU;YAAE,OAAO;QAExB,8BAA8B;QAC9B,UAAU,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;YACvC,MAAM,MAAM,GAAG,CAAC,CAAC,MAA0B,CAAC;YAC5C,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,yBAAyB;QACzB,UAAU,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,EAAE;YACzC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QAEH,oBAAoB;QACpB,UAAU,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;YACtC,IAAI,CAAC,eAAe,EAAE,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEH,UAAU,CAAC,gBAAgB,CAAC,MAAM,EAAE,GAAG,EAAE;YACrC,gDAAgD;YAChD,UAAU,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,GAAG,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,oBAAoB;QACpB,MAAM,cAAc,GAAG,QAAQ,CAAC,cAAc,CAAC,gBAAgB,CAAsB,CAAC;QACtF,IAAI,cAAc,EAAE,CAAC;YACjB,cAAc,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE;gBAC5C,MAAM,MAAM,GAAG,CAAC,CAAC,MAA2B,CAAC;gBAC7C,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACnD,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC9B,CAAC,CAAC,CAAC;QACP,CAAC;QAED,0BAA0B;QAC1B,QAAQ,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;YACrC,MAAM,MAAM,GAAG,CAAC,CAAC,MAAqB,CAAC;YACvC,IAAI,MAAM,CAAC,OAAO,CAAC,oCAAoC,CAAC,EAAE,CAAC;gBACvD,IAAI,CAAC,uBAAuB,EAAE,CAAC;YACnC,CAAC;iBAAM,IAAI,MAAM,CAAC,OAAO,CAAC,gCAAgC,CAAC,EAAE,CAAC;gBAC1D,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC/B,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,KAAa;QACnC,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACrC,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,KAAK,IAAI,EAAE;YACvC,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;gBACpB,IAAI,CAAC,WAAW,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBACjE,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBACzB,IAAI,CAAC,eAAe,EAAE,CAAC;YAC3B,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,eAAe,EAAE,CAAC;YAC3B,CAAC;QACL,CAAC,EAAE,GAAG,CAAC,CAAC;IACZ,CAAC;IAEO,aAAa,CAAC,CAAgB;QAClC,IAAI,CAAC,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACrF,OAAO;QACX,CAAC;QAED,QAAQ,CAAC,CAAC,GAAG,EAAE,CAAC;YACZ,KAAK,WAAW;gBACZ,CAAC,CAAC,cAAc,EAAE,CAAC;gBACnB,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;gBAC5B,MAAM;YACV,KAAK,SAAS;gBACV,CAAC,CAAC,cAAc,EAAE,CAAC;gBACnB,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7B,MAAM;YACV,KAAK,OAAO;gBACR,CAAC,CAAC,cAAc,EAAE,CAAC;gBACnB,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBAC/B,MAAM;YACV,KAAK,QAAQ;gBACT,IAAI,CAAC,eAAe,EAAE,CAAC;gBACvB,MAAM;QACd,CAAC;IACL,CAAC;IAEO,mBAAmB,CAAC,SAAiB;QACzC,MAAM,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,EAAE,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;QAC1F,IAAI,CAAC,kBAAkB,IAAI,kBAAkB,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO;QAEnE,2BAA2B;QAC3B,IAAI,IAAI,CAAC,sBAAsB,IAAI,CAAC,EAAE,CAAC;YACnC,kBAAkB,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QACpF,CAAC;QAED,sBAAsB;QACtB,IAAI,CAAC,sBAAsB,IAAI,SAAS,CAAC;QACzC,IAAI,IAAI,CAAC,sBAAsB,GAAG,CAAC,EAAE,CAAC;YAClC,IAAI,CAAC,sBAAsB,GAAG,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC;QAChE,CAAC;aAAM,IAAI,IAAI,CAAC,sBAAsB,IAAI,kBAAkB,CAAC,MAAM,EAAE,CAAC;YAClE,IAAI,CAAC,sBAAsB,GAAG,CAAC,CAAC;QACpC,CAAC;QAED,2BAA2B;QAC3B,kBAAkB,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAE7E,qCAAqC;QACrC,MAAM,UAAU,GAAG,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAqB,CAAC;QAC7E,IAAI,UAAU,EAAE,CAAC;YACb,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,KAAK,CAAC;QAC3E,CAAC;IACL,CAAC;IAEO,uBAAuB;QAC3B,IAAI,IAAI,CAAC,sBAAsB,IAAI,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,sBAAsB,CAAC,EAAE,CAAC;YACpF,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YACjE,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACzC,CAAC;aAAM,CAAC;YACJ,gCAAgC;YAChC,MAAM,UAAU,GAAG,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAqB,CAAC;YAC7E,IAAI,UAAU,EAAE,CAAC;gBACb,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YACzC,CAAC;QACL,CAAC;QACD,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAEO,aAAa,CAAC,KAAa;QAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAErD,qBAAqB;QACrB,MAAM,UAAU,GAAG,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAqB,CAAC;QAC7E,IAAI,UAAU,EAAE,CAAC;YACb,UAAU,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;QACpE,CAAC;QAED,WAAW;QACX,IAAK,MAAc,CAAC,gBAAgB,EAAE,CAAC;YAClC,MAAc,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC5D,CAAC;IACL,CAAC;IAEO,iBAAiB;QACrB,IAAI,CAAC,IAAI,CAAC,mBAAmB;YAAE,OAAO;QAEtC,MAAM,eAAe,GAAG,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAC;QACpF,IAAI,CAAC,eAAe;YAAE,OAAO;QAE7B,eAAe,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,KAAK,EAAE,EAAE,CAAC;uDACzB,KAAK;;sBAEtC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC;;;qDAGR,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC;sBAChE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,kCAAkC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;;+CAE3E,UAAU,CAAC,IAAI;;SAErD,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAEZ,sBAAsB;QACtB,eAAe,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YACzE,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;gBAChC,IAAI,CAAC,sBAAsB,GAAG,KAAK,CAAC;gBACpC,IAAI,CAAC,uBAAuB,EAAE,CAAC;YACnC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,sBAAsB,GAAG,CAAC,CAAC,CAAC;IACrC,CAAC;IAEO,iBAAiB,CAAC,IAAY;QAClC,QAAQ,IAAI,EAAE,CAAC;YACX,KAAK,SAAS;gBACV,OAAO,IAAI,CAAC;YAChB,KAAK,UAAU;gBACX,OAAO,GAAG,CAAC;YACf,KAAK,YAAY,CAAC;YAClB;gBACI,OAAO,IAAI,CAAC;QACpB,CAAC;IACL,CAAC;IAEO,eAAe;QACnB,IAAI,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1D,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACxD,CAAC;IACL,CAAC;IAEO,eAAe;QACnB,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACjD,IAAI,CAAC,sBAAsB,GAAG,CAAC,CAAC,CAAC;QACrC,CAAC;IACL,CAAC;IAEO,kBAAkB;QACtB,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC;QACxD,MAAM,cAAc,GAAG,QAAQ,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;QAElE,IAAI,cAAc,EAAE,CAAC;YACjB,cAAc,CAAC,SAAS,GAAG;;0BAEb,QAAQ,CAAC,IAAI;sDACe,QAAQ,CAAC,WAAW;;qEAEL,QAAQ,CAAC,aAAa;;qEAEtB,QAAQ,CAAC,aAAa,GAAG,EAAE;;;;;;8BAMlE,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,OAAO,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;;;;aAIjF,CAAC;QACN,CAAC;IACL,CAAC;IAEO,uBAAuB;QAC3B,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;QACjD,MAAM,QAAQ,GAAG,CAAC,QAAQ,CAAC,iBAAiB,CAAC;QAE7C,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,EAAE,iBAAiB,EAAE,QAAQ,EAAE,CAAC,CAAC;QAElE,6BAA6B;QAC7B,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,oCAAoC,CAAC,CAAC;QAC5E,IAAI,MAAM,EAAE,CAAC;YACT,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAChD,CAAC;IACL,CAAC;IAEO,mBAAmB;QACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;QACjD,MAAM,QAAQ,GAAG,CAAC,QAAQ,CAAC,aAAa,CAAC;QAEzC,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,EAAE,aAAa,EAAE,QAAQ,EAAE,CAAC,CAAC;QAE9D,6BAA6B;QAC7B,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,gCAAgC,CAAC,CAAC;QACxE,IAAI,MAAM,EAAE,CAAC;YACT,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAChD,CAAC;IACL,CAAC;IAEO,UAAU,CAAC,IAAY;QAC3B,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC1C,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC;QACvB,OAAO,GAAG,CAAC,SAAS,CAAC;IACzB,CAAC;IAEM,OAAO;QACV,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACrC,CAAC;QAED,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,CAAC;QACtC,CAAC;IACL,CAAC;CACJ;AA3VD,4BA2VC"}