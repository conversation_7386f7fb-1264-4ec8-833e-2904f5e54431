{"version": 3, "file": "CrossPlatformCoordinator.d.ts", "sourceRoot": "", "sources": ["../../src/crossplatform/CrossPlatformCoordinator.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AAGtC,MAAM,WAAW,aAAa;IAC1B,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,SAAS,GAAG,QAAQ,GAAG,QAAQ,GAAG,KAAK,GAAG,UAAU,GAAG,UAAU,CAAC;IACxE,QAAQ,EAAE,SAAS,GAAG,OAAO,GAAG,OAAO,GAAG,SAAS,GAAG,KAAK,GAAG,UAAU,CAAC;IACzE,YAAY,EAAE,kBAAkB,CAAC;IACjC,QAAQ,EAAE,cAAc,CAAC;IACzB,WAAW,EAAE,WAAW,CAAC;IACzB,QAAQ,EAAE,MAAM,CAAC;IACjB,UAAU,EAAE,MAAM,CAAC;IACnB,gBAAgB,EAAE,aAAa,GAAG,aAAa,GAAG,UAAU,CAAC;CAChE;AAED,MAAM,WAAW,kBAAkB;IAC/B,sBAAsB,EAAE,MAAM,EAAE,CAAC;IACjC,YAAY,EAAE,MAAM,CAAC;IACrB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,eAAe,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,WAAW,CAAC;IACzD,eAAe,EAAE,MAAM,CAAC;IACxB,YAAY,EAAE,CAAC,MAAM,GAAG,UAAU,GAAG,UAAU,GAAG,WAAW,CAAC,EAAE,CAAC;IACjE,OAAO,EAAE,MAAM,EAAE,CAAC;IAClB,mBAAmB,CAAC,EAAE;QAClB,UAAU,EAAE;YAAE,KAAK,EAAE,MAAM,CAAC;YAAC,MAAM,EAAE,MAAM,CAAA;SAAE,CAAC;QAC9C,UAAU,EAAE,MAAM,CAAC;QACnB,WAAW,EAAE,MAAM,CAAC;KACvB,CAAC;CACL;AAED,MAAM,WAAW,cAAc;IAC3B,QAAQ,EAAE,MAAM,CAAC;IACjB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,WAAW,CAAC,EAAE;QAAE,GAAG,EAAE,MAAM,CAAC;QAAC,GAAG,EAAE,MAAM,CAAA;KAAE,CAAC;IAC3C,eAAe,EAAE,MAAM,GAAG,MAAM,GAAG,QAAQ,GAAG,QAAQ,GAAG,SAAS,CAAC;CACtE;AAED,MAAM,WAAW,WAAW;IACxB,SAAS,EAAE,MAAM,CAAC;IAClB,WAAW,EAAE,MAAM,GAAG,UAAU,GAAG,UAAU,GAAG,KAAK,CAAC;IACtD,iBAAiB,EAAE,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,WAAW,CAAC;IAC1D,OAAO,EAAE,MAAM,CAAC;IAChB,SAAS,EAAE;QAAE,MAAM,EAAE,MAAM,CAAC;QAAC,QAAQ,EAAE,MAAM,CAAA;KAAE,CAAC;IAChD,SAAS,EAAE,OAAO,CAAC;CACtB;AAED,MAAM,WAAW,mBAAmB;IAChC,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,MAAM,CAAC;IACb,WAAW,EAAE,MAAM,CAAC;IACpB,aAAa,EAAE,MAAM,EAAE,CAAC;IACxB,eAAe,EAAE,MAAM,CAAC;IACxB,eAAe,EAAE,MAAM,CAAC;IACxB,QAAQ,EAAE,MAAM,CAAC;IACjB,YAAY,EAAE;QACV,MAAM,EAAE,cAAc,GAAG,WAAW,GAAG,QAAQ,GAAG,UAAU,CAAC;QAC7D,wBAAwB,EAAE,MAAM,CAAC;QACjC,cAAc,EAAE,MAAM,CAAC;KAC1B,CAAC;IACF,UAAU,EAAE,oBAAoB,EAAE,CAAC;IACnC,YAAY,EAAE;QACV,YAAY,EAAE,MAAM,CAAC;QACrB,eAAe,CAAC,EAAE,MAAM,CAAC;QACzB,oBAAoB,EAAE,MAAM,EAAE,CAAC;QAC/B,YAAY,EAAE,MAAM,EAAE,CAAC;KAC1B,CAAC;CACL;AAED,MAAM,WAAW,oBAAoB;IACjC,IAAI,EAAE,cAAc,GAAG,WAAW,GAAG,iBAAiB,GAAG,aAAa,GAAG,kBAAkB,CAAC;IAC5F,QAAQ,EAAE,MAAM,CAAC;IACjB,UAAU,EAAE;QACR,SAAS,EAAE,MAAM,CAAC;QAClB,QAAQ,EAAE,MAAM,CAAC;QACjB,SAAS,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,CAAC;QACrC,OAAO,CAAC,EAAE,MAAM,EAAE,CAAC;KACtB,CAAC;IACF,mBAAmB,EAAE;QACjB,eAAe,EAAE,MAAM,CAAC;QACxB,YAAY,EAAE,OAAO,CAAC;QACtB,mBAAmB,EAAE,OAAO,CAAC;QAC7B,cAAc,EAAE,OAAO,CAAC;KAC3B,CAAC;CACL;AAED,MAAM,WAAW,oBAAoB;IACjC,EAAE,EAAE,MAAM,CAAC;IACX,WAAW,EAAE,MAAM,CAAC;IACpB,YAAY,EAAE,GAAG,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;IACzC,OAAO,EAAE,mBAAmB,CAAC;IAC7B,MAAM,EAAE,cAAc,GAAG,cAAc,GAAG,WAAW,GAAG,WAAW,GAAG,QAAQ,CAAC;IAC/E,SAAS,EAAE,MAAM,CAAC;IAClB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,mBAAmB,EAAE;QACjB,aAAa,EAAE,MAAM,CAAC;QACtB,SAAS,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC/B,cAAc,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;KACvC,CAAC;IACF,OAAO,EAAE;QACL,iBAAiB,EAAE,MAAM,CAAC;QAC1B,uBAAuB,EAAE,MAAM,CAAC;QAChC,YAAY,EAAE,MAAM,CAAC;QACrB,YAAY,EAAE,MAAM,CAAC;KACxB,CAAC;CACL;AAED,qBAAa,wBAAyB,SAAQ,YAAY;IACtD,OAAO,CAAC,QAAQ,CAAS;IACzB,OAAO,CAAC,aAAa,CAAgB;IACrC,OAAO,CAAC,YAAY,CAAyC;IAC7D,OAAO,CAAC,oBAAoB,CAA+C;IAC3E,OAAO,CAAC,cAAc,CAAgD;IACtE,OAAO,CAAC,gBAAgB,CAA2B;IACnD,OAAO,CAAC,aAAa,CAAkB;IACvC,OAAO,CAAC,iBAAiB,CAA+B;IACxD,OAAO,CAAC,uBAAuB,CAA+B;;IAUxD,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IAmBjC,OAAO,CAAC,gBAAgB;IAMxB,OAAO,CAAC,oBAAoB;IAY5B,OAAO,CAAC,mBAAmB;IAsC3B,OAAO,CAAC,kBAAkB;IAsB1B,OAAO,CAAC,cAAc;IAOtB,OAAO,CAAC,iBAAiB;IAWzB,OAAO,CAAC,8BAA8B;YAqJxB,mBAAmB;YAoBnB,uBAAuB;IAcrC,OAAO,CAAC,oBAAoB;YAMd,eAAe;IAkB7B,OAAO,CAAC,2BAA2B;YAMrB,0BAA0B;IAYlC,uBAAuB,CAAC,SAAS,EAAE,MAAM,EAAE,aAAa,CAAC,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC;YA6C7E,kBAAkB;IAyBhC,OAAO,CAAC,kBAAkB;YAiCZ,iBAAiB;YAsBjB,sBAAsB;YAoBtB,4BAA4B;YAsB5B,eAAe;YAoBf,qBAAqB;YAgBrB,wBAAwB;YAqBxB,2BAA2B;YAsB3B,qBAAqB;IAQnC,OAAO,CAAC,oBAAoB;IAsB5B,qBAAqB,IAAI;QACrB,aAAa,EAAE,aAAa,CAAC;QAC7B,YAAY,EAAE,MAAM,CAAC;QACrB,cAAc,EAAE,MAAM,CAAC;QACvB,aAAa,EAAE,OAAO,CAAC;QACvB,iBAAiB,EAAE,MAAM,EAAE,CAAC;KAC/B;IAUD,gBAAgB,CAAC,SAAS,EAAE,MAAM,GAAG,oBAAoB,GAAG,IAAI;IAI1D,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;CAejC"}