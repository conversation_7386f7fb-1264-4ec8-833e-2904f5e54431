"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
class PhantomBrowserTest {
    constructor() {
        this.mainWindow = null;
    }
    async initialize() {
        console.log('Initializing Phantom Browser Test...');
        await electron_1.app.whenReady();
        console.log('Electron app ready');
        this.createMainWindow();
        this.setupEventHandlers();
        console.log('Phantom Browser Test initialized successfully');
    }
    createMainWindow() {
        console.log('Creating main window...');
        this.mainWindow = new electron_1.BrowserWindow({
            width: 1200,
            height: 800,
            minWidth: 800,
            minHeight: 600,
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                webSecurity: false,
                allowRunningInsecureContent: false
            },
            titleBarStyle: 'default',
            show: true,
            backgroundColor: '#ffffff',
            center: true,
            resizable: true,
            maximizable: true,
            minimizable: true,
            closable: true,
            alwaysOnTop: false,
            skipTaskbar: false
        });
        console.log('Window created successfully');
        // Load a simple HTML content
        const htmlContent = `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Phantom Browser - Test</title>
            <style>
                body {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    margin: 0;
                    padding: 40px;
                    min-height: 100vh;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    text-align: center;
                }
                .container {
                    max-width: 600px;
                    background: rgba(255, 255, 255, 0.1);
                    padding: 40px;
                    border-radius: 20px;
                    backdrop-filter: blur(10px);
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                }
                h1 {
                    font-size: 3em;
                    margin-bottom: 20px;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
                }
                .subtitle {
                    font-size: 1.2em;
                    margin-bottom: 30px;
                    opacity: 0.9;
                }
                .status {
                    background: rgba(40, 167, 69, 0.8);
                    padding: 15px 30px;
                    border-radius: 50px;
                    font-weight: bold;
                    margin: 20px 0;
                    display: inline-block;
                }
                .features {
                    text-align: left;
                    margin-top: 30px;
                }
                .feature {
                    margin: 10px 0;
                    padding: 10px;
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 10px;
                }
                .feature::before {
                    content: "✓ ";
                    color: #28a745;
                    font-weight: bold;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🛡️ Phantom Browser</h1>
                <div class="subtitle">Advanced Privacy Protection Browser</div>
                <div class="status">✅ APPLICATION RUNNING SUCCESSFULLY</div>
                
                <div class="features">
                    <div class="feature">Window Creation: Working</div>
                    <div class="feature">Electron Framework: Operational</div>
                    <div class="feature">UI Rendering: Functional</div>
                    <div class="feature">Event Handling: Active</div>
                    <div class="feature">Cross-Platform: Ready</div>
                </div>
                
                <p style="margin-top: 30px; opacity: 0.8;">
                    This is a test version to verify the application window displays correctly.
                    All steganographic features are available in the full version.
                </p>
                
                <p style="margin-top: 20px; font-size: 0.9em; opacity: 0.7;">
                    Version: 1.0.0 | Build: Test | Platform: Windows x64
                </p>
            </div>
            
            <script>
                console.log('Phantom Browser Test UI loaded successfully');
                
                // Simple interaction test
                document.addEventListener('click', function() {
                    console.log('User interaction detected');
                });
                
                // Window focus test
                window.addEventListener('focus', function() {
                    console.log('Window focused');
                });
                
                // Startup time measurement
                const startTime = performance.now();
                window.addEventListener('load', function() {
                    const loadTime = performance.now() - startTime;
                    console.log('Page load time:', loadTime.toFixed(2), 'ms');
                });
            </script>
        </body>
        </html>
        `;
        this.mainWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(htmlContent)}`);
        // Event handlers
        this.mainWindow.once('ready-to-show', () => {
            console.log('Window ready-to-show event fired');
            this.mainWindow?.show();
            this.mainWindow?.focus();
            console.log('Window shown and focused');
        });
        this.mainWindow.on('closed', () => {
            console.log('Window closed');
            this.mainWindow = null;
        });
        this.mainWindow.on('show', () => {
            console.log('Window show event fired');
        });
        this.mainWindow.on('focus', () => {
            console.log('Window focus event fired');
        });
        this.mainWindow.webContents.on('did-finish-load', () => {
            console.log('WebContents did-finish-load event fired');
        });
        this.mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
            console.error('WebContents did-fail-load:', errorCode, errorDescription);
        });
        this.mainWindow.webContents.on('dom-ready', () => {
            console.log('WebContents DOM ready');
        });
        console.log('Window setup complete');
    }
    setupEventHandlers() {
        electron_1.app.on('window-all-closed', () => {
            console.log('All windows closed');
            if (process.platform !== 'darwin') {
                electron_1.app.quit();
            }
        });
        electron_1.app.on('activate', () => {
            if (electron_1.BrowserWindow.getAllWindows().length === 0) {
                this.createMainWindow();
            }
        });
    }
}
// Initialize the test browser
console.log('Starting Phantom Browser Test...');
const phantomBrowserTest = new PhantomBrowserTest();
phantomBrowserTest.initialize().catch((error) => {
    console.error('Failed to initialize Phantom Browser Test:', error);
    process.exit(1);
});
// Handle app events
electron_1.app.on('before-quit', () => {
    console.log('Application quitting...');
});
process.on('uncaughtException', (error) => {
    console.error('Uncaught exception:', error);
});
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled rejection at:', promise, 'reason:', reason);
});
console.log('Phantom Browser Test script loaded');
