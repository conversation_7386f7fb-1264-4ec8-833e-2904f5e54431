export interface ProxyConfig {
    type: 'http' | 'https' | 'socks4' | 'socks5' | 'direct';
    host: string;
    port: number;
    username?: string;
    password?: string;
    enabled: boolean;
}
export interface VPNConfig {
    provider: string;
    server: string;
    protocol: 'openvpn' | 'wireguard' | 'ikev2';
    credentials: {
        username?: string;
        password?: string;
        certificate?: string;
        privateKey?: string;
    };
    enabled: boolean;
}
export declare class ProxyManager {
    private currentProxy;
    private proxyList;
    private rotationEnabled;
    private rotationInterval;
    private vpnConfig;
    constructor();
    initialize(): Promise<void>;
    private initializeDefaultProxies;
    private loadProxyList;
    setProxy(config: ProxyConfig): Promise<void>;
    clearProxy(): Promise<void>;
    testProxy(config: ProxyConfig): Promise<boolean>;
    private testSocksProxy;
    private testHttpProxy;
    rotateProxy(): Promise<void>;
    enableProxyRotation(intervalMinutes?: number): void;
    disableProxyRotation(): void;
    addProxy(config: ProxyConfig): void;
    removeProxy(host: string, port: number): void;
    getProxyList(): ProxyConfig[];
    getCurrentProxy(): ProxyConfig | null;
    setupDNSOverHTTPS(): Promise<void>;
    setupTrafficObfuscation(): Promise<void>;
    connectVPN(config: VPNConfig): Promise<void>;
    disconnectVPN(): Promise<void>;
    getVPNStatus(): {
        connected: boolean;
        config?: VPNConfig;
    };
    destroy(): void;
}
//# sourceMappingURL=ProxyManager.d.ts.map