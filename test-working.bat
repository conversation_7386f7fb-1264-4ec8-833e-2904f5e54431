@echo off
echo Testing Fixed Phantom Browser Executable...
echo.

cd "executable-working\phantom-browser-working-win32-x64"

echo Current directory: %CD%
echo.

echo Checking if executable exists...
if exist "phantom-browser-working.exe" (
    echo ✓ Executable found: phantom-browser-working.exe
    dir "phantom-browser-working.exe"
) else (
    echo ✗ Executable not found!
    exit /b 1
)

echo.
echo Launching Fixed Phantom Browser with console output...
echo Press Ctrl+C to stop if needed
echo.

start /wait "Phantom Browser Working Test" "phantom-browser-working.exe" --enable-logging --log-level=0 --disable-gpu-sandbox --no-sandbox

echo.
echo Application has exited.
pause
