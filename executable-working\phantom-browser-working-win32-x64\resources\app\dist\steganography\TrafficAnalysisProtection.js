"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.TrafficAnalysisProtection = void 0;
const electron_1 = require("electron");
const crypto = __importStar(require("crypto"));
class TrafficAnalysisProtection {
    constructor() {
        this.networkFlows = [];
        this.packetSizeDistribution = [];
        this.timingDistribution = [];
        this.dummyTrafficInterval = null;
        this.settings = {
            enablePacketSizeObfuscation: true,
            enableTimingObfuscation: true,
            enableFlowObfuscation: true,
            enableBurstObfuscation: true,
            enableDirectionObfuscation: true,
            obfuscationLevel: 8
        };
        this.initializeDistributions();
    }
    async initialize() {
        await this.setupPacketSizeObfuscation();
        await this.setupTimingObfuscation();
        await this.setupFlowObfuscation();
        await this.setupBurstObfuscation();
        await this.setupDirectionObfuscation();
        this.startDummyTraffic();
    }
    initializeDistributions() {
        // Initialize realistic packet size distribution (based on common web traffic)
        this.packetSizeDistribution = [
            64, 128, 256, 512, 1024, 1460, 1500, 2048, 4096, 8192
        ];
        // Initialize realistic timing distribution (in milliseconds)
        this.timingDistribution = [
            10, 25, 50, 100, 200, 500, 1000, 2000, 5000
        ];
    }
    async setupPacketSizeObfuscation() {
        if (!this.settings.enablePacketSizeObfuscation)
            return;
        const ses = electron_1.session.defaultSession;
        // Intercept requests to add size obfuscation
        ses.webRequest.onBeforeRequest({ urls: ['<all_urls>'] }, (details, callback) => {
            if (this.shouldObfuscatePacketSize()) {
                // Add padding to normalize packet sizes
                const padding = this.generateSizePadding(details.url);
                if (padding && details.uploadData) {
                    // Add padding to upload data
                    details.uploadData.push({
                        bytes: Buffer.from(padding)
                    });
                }
            }
            callback({ cancel: false });
        });
        // Intercept responses to add size obfuscation
        ses.webRequest.onHeadersReceived({ urls: ['<all_urls>'] }, (details, callback) => {
            if (this.shouldObfuscatePacketSize() && details.responseHeaders) {
                // Add padding headers to obfuscate response size
                const paddingHeader = this.generateResponsePadding();
                details.responseHeaders['X-Padding-Data'] = [paddingHeader];
            }
            callback({ responseHeaders: details.responseHeaders });
        });
    }
    shouldObfuscatePacketSize() {
        return Math.random() < (this.settings.obfuscationLevel / 10);
    }
    generateSizePadding(url) {
        // Generate padding based on URL type and obfuscation level
        const baseSize = this.getBasePaddingSize(url);
        const randomFactor = Math.random() * this.settings.obfuscationLevel / 10;
        const paddingSize = Math.floor(baseSize * randomFactor);
        if (paddingSize > 0) {
            return crypto.randomBytes(Math.min(paddingSize, 1024)).toString('base64');
        }
        return null;
    }
    getBasePaddingSize(url) {
        // Different padding strategies for different content types
        if (url.includes('.js') || url.includes('.css')) {
            return 256; // Moderate padding for scripts/styles
        }
        else if (url.includes('.jpg') || url.includes('.png')) {
            return 512; // Larger padding for images
        }
        else if (url.includes('api/') || url.includes('ajax')) {
            return 128; // Small padding for API calls
        }
        else {
            return 384; // Default padding for HTML pages
        }
    }
    generateResponsePadding() {
        const paddingSize = Math.floor(Math.random() * 512) + 64; // 64-576 bytes
        return crypto.randomBytes(paddingSize).toString('base64').substring(0, 100);
    }
    async setupTimingObfuscation() {
        if (!this.settings.enableTimingObfuscation)
            return;
        const ses = electron_1.session.defaultSession;
        // Add variable delays to requests to obfuscate timing patterns
        ses.webRequest.onBeforeRequest({ urls: ['<all_urls>'] }, (details, callback) => {
            const delay = this.calculateTimingDelay(details.url);
            setTimeout(() => {
                callback({ cancel: false });
            }, delay);
        });
    }
    calculateTimingDelay(url) {
        // Calculate delay based on obfuscation level and content type
        const baseDelay = this.getBaseTimingDelay(url);
        const obfuscationFactor = this.settings.obfuscationLevel / 10;
        const randomFactor = (Math.random() - 0.5) * 2; // -1 to 1
        const delay = baseDelay + (baseDelay * obfuscationFactor * randomFactor * 0.5);
        return Math.max(0, Math.floor(delay));
    }
    getBaseTimingDelay(url) {
        // Base delays for different content types
        if (url.includes('.css') || url.includes('.js')) {
            return 20; // Fast for critical resources
        }
        else if (url.includes('.jpg') || url.includes('.png')) {
            return 50; // Medium for images
        }
        else if (url.includes('api/')) {
            return 100; // Longer for API calls
        }
        else {
            return 150; // Longest for page requests
        }
    }
    async setupFlowObfuscation() {
        if (!this.settings.enableFlowObfuscation)
            return;
        // Monitor and obfuscate network flows
        setInterval(() => {
            this.analyzeAndObfuscateFlows();
        }, 5000); // Every 5 seconds
    }
    analyzeAndObfuscateFlows() {
        // Analyze current network flows and inject obfuscation
        const currentTime = Date.now();
        // Remove old flows (older than 1 minute)
        this.networkFlows = this.networkFlows.filter(flow => currentTime - flow.timestamp < 60000);
        // Analyze flow patterns
        const flowAnalysis = this.analyzeFlowPatterns();
        // Generate counter-flows if needed
        if (flowAnalysis.needsObfuscation) {
            this.generateCounterFlows(flowAnalysis);
        }
    }
    analyzeFlowPatterns() {
        // Simple flow pattern analysis
        const recentFlows = this.networkFlows.filter(flow => Date.now() - flow.timestamp < 10000 // Last 10 seconds
        );
        const patterns = [];
        let needsObfuscation = false;
        // Check for suspicious patterns
        if (recentFlows.length > 20) {
            patterns.push('high_frequency');
            needsObfuscation = true;
        }
        const avgSize = recentFlows.reduce((sum, flow) => sum + flow.size, 0) / recentFlows.length;
        if (avgSize > 10000) {
            patterns.push('large_transfers');
            needsObfuscation = true;
        }
        return { needsObfuscation, patterns };
    }
    generateCounterFlows(analysis) {
        // Generate counter-flows to obfuscate real traffic patterns
        analysis.patterns.forEach(pattern => {
            switch (pattern) {
                case 'high_frequency':
                    this.generateLowFrequencyCounterFlow();
                    break;
                case 'large_transfers':
                    this.generateSmallTransferCounterFlow();
                    break;
            }
        });
    }
    generateLowFrequencyCounterFlow() {
        // Generate slow, steady traffic to mask high-frequency bursts
        setTimeout(() => {
            this.generateDummyRequest('low_frequency');
        }, 2000 + Math.random() * 3000);
    }
    generateSmallTransferCounterFlow() {
        // Generate small requests to mask large transfers
        for (let i = 0; i < 3; i++) {
            setTimeout(() => {
                this.generateDummyRequest('small_transfer');
            }, i * 500);
        }
    }
    async setupBurstObfuscation() {
        if (!this.settings.enableBurstObfuscation)
            return;
        // Monitor for traffic bursts and add obfuscation
        setInterval(() => {
            this.detectAndObfuscateBursts();
        }, 1000); // Every second
    }
    detectAndObfuscateBursts() {
        const recentFlows = this.networkFlows.filter(flow => Date.now() - flow.timestamp < 2000 // Last 2 seconds
        );
        // Detect burst patterns
        if (recentFlows.length > 10) {
            // High burst detected - add counter-burst
            this.generateCounterBurst();
        }
    }
    generateCounterBurst() {
        // Generate a counter-burst with different characteristics
        const burstSize = 3 + Math.floor(Math.random() * 5);
        for (let i = 0; i < burstSize; i++) {
            setTimeout(() => {
                this.generateDummyRequest('counter_burst');
            }, i * 100 + Math.random() * 200);
        }
    }
    async setupDirectionObfuscation() {
        if (!this.settings.enableDirectionObfuscation)
            return;
        // Add bidirectional dummy traffic to obfuscate traffic direction analysis
        setInterval(() => {
            this.generateBidirectionalTraffic();
        }, 10000 + Math.random() * 20000); // Every 10-30 seconds
    }
    generateBidirectionalTraffic() {
        // Generate both inbound and outbound dummy traffic
        this.generateDummyRequest('outbound');
        setTimeout(() => {
            this.generateDummyRequest('inbound');
        }, 500 + Math.random() * 1000);
    }
    startDummyTraffic() {
        if (this.settings.obfuscationLevel < 5)
            return;
        // Start continuous dummy traffic generation
        this.dummyTrafficInterval = setInterval(() => {
            this.generateDummyRequest('background');
        }, 30000 + Math.random() * 60000); // Every 30-90 seconds
    }
    generateDummyRequest(type) {
        // Generate dummy HTTP requests to obfuscate real traffic
        const dummyEndpoints = [
            'https://httpbin.org/delay/1',
            'https://jsonplaceholder.typicode.com/posts/1',
            'https://httpstat.us/200',
            'https://reqres.in/api/users/1'
        ];
        const randomEndpoint = dummyEndpoints[Math.floor(Math.random() * dummyEndpoints.length)];
        fetch(randomEndpoint, {
            method: 'GET',
            mode: 'no-cors',
            headers: {
                'X-Dummy-Traffic': type,
                'X-Timestamp': Date.now().toString()
            }
        }).catch(() => {
            // Ignore errors - this is dummy traffic
        });
        // Record the dummy flow
        this.recordNetworkFlow({
            sourceIP: '127.0.0.1',
            destIP: 'dummy',
            sourcePort: Math.floor(Math.random() * 65535),
            destPort: 443,
            protocol: 'HTTPS',
            timestamp: Date.now(),
            size: 100 + Math.random() * 500,
            direction: 'outbound'
        });
    }
    recordNetworkFlow(flow) {
        this.networkFlows.push(flow);
        // Keep only recent flows (last 5 minutes)
        const fiveMinutesAgo = Date.now() - 300000;
        this.networkFlows = this.networkFlows.filter(f => f.timestamp > fiveMinutesAgo);
    }
    getTrafficStatistics() {
        const recentFlows = this.networkFlows.filter(flow => Date.now() - flow.timestamp < 60000 // Last minute
        );
        const totalFlows = recentFlows.length;
        const avgPacketSize = totalFlows > 0
            ? recentFlows.reduce((sum, flow) => sum + flow.size, 0) / totalFlows
            : 0;
        const dummyFlows = recentFlows.filter(flow => flow.destIP === 'dummy').length;
        const obfuscationRatio = totalFlows > 0 ? dummyFlows / totalFlows : 0;
        return {
            totalFlows,
            avgPacketSize,
            avgTiming: this.calculateAverageTiming(),
            obfuscationRatio
        };
    }
    calculateAverageTiming() {
        // Calculate average timing between requests
        if (this.networkFlows.length < 2)
            return 0;
        const sortedFlows = this.networkFlows.sort((a, b) => a.timestamp - b.timestamp);
        let totalGaps = 0;
        let gapCount = 0;
        for (let i = 1; i < sortedFlows.length; i++) {
            const gap = sortedFlows[i].timestamp - sortedFlows[i - 1].timestamp;
            totalGaps += gap;
            gapCount++;
        }
        return gapCount > 0 ? totalGaps / gapCount : 0;
    }
    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
    }
    getSettings() {
        return { ...this.settings };
    }
    destroy() {
        if (this.dummyTrafficInterval) {
            clearInterval(this.dummyTrafficInterval);
            this.dummyTrafficInterval = null;
        }
    }
}
exports.TrafficAnalysisProtection = TrafficAnalysisProtection;
//# sourceMappingURL=TrafficAnalysisProtection.js.map