export interface TrafficAnalysisSettings {
    enablePacketSizeObfuscation: boolean;
    enableTimingObfuscation: boolean;
    enableFlowObfuscation: boolean;
    enableBurstObfuscation: boolean;
    enableDirectionObfuscation: boolean;
    obfuscationLevel: number;
}
export interface NetworkFlow {
    sourceIP: string;
    destIP: string;
    sourcePort: number;
    destPort: number;
    protocol: string;
    timestamp: number;
    size: number;
    direction: 'inbound' | 'outbound';
}
export declare class TrafficAnalysisProtection {
    private settings;
    private networkFlows;
    private packetSizeDistribution;
    private timingDistribution;
    private dummyTrafficInterval;
    constructor();
    initialize(): Promise<void>;
    private initializeDistributions;
    private setupPacketSizeObfuscation;
    private shouldObfuscatePacketSize;
    private generateSizePadding;
    private getBasePaddingSize;
    private generateResponsePadding;
    private setupTimingObfuscation;
    private calculateTimingDelay;
    private getBaseTimingDelay;
    private setupFlowObfuscation;
    private analyzeAndObfuscateFlows;
    private analyzeFlowPatterns;
    private generateCounterFlows;
    private generateLowFrequencyCounterFlow;
    private generateSmallTransferCounterFlow;
    private setupBurstObfuscation;
    private detectAndObfuscateBursts;
    private generateCounterBurst;
    private setupDirectionObfuscation;
    private generateBidirectionalTraffic;
    private startDummyTraffic;
    private generateDummyRequest;
    private recordNetworkFlow;
    getTrafficStatistics(): {
        totalFlows: number;
        avgPacketSize: number;
        avgTiming: number;
        obfuscationRatio: number;
    };
    private calculateAverageTiming;
    updateSettings(newSettings: Partial<TrafficAnalysisSettings>): void;
    getSettings(): TrafficAnalysisSettings;
    destroy(): void;
}
//# sourceMappingURL=TrafficAnalysisProtection.d.ts.map