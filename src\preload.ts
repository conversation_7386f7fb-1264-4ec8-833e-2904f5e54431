import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';

// Expose secure API to renderer process
contextBridge.exposeInMainWorld('phantomAPI', {
    // Navigation
    navigate: (url: string) => ipcRenderer.invoke('navigate', url),
    goBack: () => ipcRenderer.invoke('go-back'),
    goForward: () => ipcRenderer.invoke('go-forward'),
    reload: () => ipcRenderer.invoke('reload'),
    
    // Tab management
    createTab: (url?: string) => ipcRenderer.invoke('create-tab', url),
    closeTab: (tabId: string) => ipcRenderer.invoke('close-tab', tabId),
    switchTab: (tabId: string) => ipcRenderer.invoke('switch-tab', tabId),
    getTabs: () => ipcRenderer.invoke('get-tabs'),
    
    // Privacy settings
    getPrivacySettings: () => ipcRenderer.invoke('get-privacy-settings'),
    updatePrivacySettings: (settings: any) => ipcRenderer.invoke('update-privacy-settings', settings),
    clearBrowsingData: () => ipcRenderer.invoke('clear-browsing-data'),
    
    // Fingerprint protection
    getFingerprintProfile: () => ipcRenderer.invoke('get-fingerprint-profile'),
    setFingerprintProfile: (profile: any) => ipcRenderer.invoke('set-fingerprint-profile', profile),
    rotateFingerprintProfile: () => ipcRenderer.invoke('rotate-fingerprint-profile'),
    
    // User agent management
    getUserAgentProfile: () => ipcRenderer.invoke('get-user-agent-profile'),
    setUserAgentProfile: (profile: any) => ipcRenderer.invoke('set-user-agent-profile', profile),
    rotateUserAgent: () => ipcRenderer.invoke('rotate-user-agent'),
    enableUserAgentRotation: () => ipcRenderer.invoke('enable-user-agent-rotation'),
    disableUserAgentRotation: () => ipcRenderer.invoke('disable-user-agent-rotation'),
    
    // Proxy management
    getProxySettings: () => ipcRenderer.invoke('get-proxy-settings'),
    setProxy: (config: any) => ipcRenderer.invoke('set-proxy', config),
    clearProxy: () => ipcRenderer.invoke('clear-proxy'),
    testProxy: (config: any) => ipcRenderer.invoke('test-proxy', config),
    enableProxyRotation: (interval: number) => ipcRenderer.invoke('enable-proxy-rotation', interval),
    disableProxyRotation: () => ipcRenderer.invoke('disable-proxy-rotation'),
    
    // Security
    getSecuritySettings: () => ipcRenderer.invoke('get-security-settings'),
    updateSecuritySettings: (settings: any) => ipcRenderer.invoke('update-security-settings', settings),
    performSecurityAudit: () => ipcRenderer.invoke('perform-security-audit'),
    
    // VPN
    connectVPN: (config: any) => ipcRenderer.invoke('connect-vpn', config),
    disconnectVPN: () => ipcRenderer.invoke('disconnect-vpn'),
    getVPNStatus: () => ipcRenderer.invoke('get-vpn-status'),
    
    // Events
    onTabCreated: (callback: (tab: any) => void) => {
        ipcRenderer.on('tab-created', (event, tab) => callback(tab));
    },
    onTabClosed: (callback: (tabId: string) => void) => {
        ipcRenderer.on('tab-closed', (event, tabId) => callback(tabId));
    },
    onTabUpdated: (callback: (tab: any) => void) => {
        ipcRenderer.on('tab-updated', (event, tab) => callback(tab));
    },
    onNavigationComplete: (callback: (data: any) => void) => {
        ipcRenderer.on('navigation-complete', (event, data) => callback(data));
    },
    onPrivacyAlert: (callback: (alert: any) => void) => {
        ipcRenderer.on('privacy-alert', (event, alert) => callback(alert));
    },
    onNavigateWebview: (callback: (url: string) => void) => {
        ipcRenderer.on('navigate-webview', (event, url) => callback(url));
    },
    
    // Utility
    openDevTools: () => ipcRenderer.invoke('open-dev-tools'),
    closeDevTools: () => ipcRenderer.invoke('close-dev-tools'),
    takeScreenshot: () => ipcRenderer.invoke('take-screenshot'),
    exportSettings: () => ipcRenderer.invoke('export-settings'),
    importSettings: (settings: any) => ipcRenderer.invoke('import-settings', settings),
    
    // Advanced features
    enableStealthMode: () => ipcRenderer.invoke('enable-stealth-mode'),
    disableStealthMode: () => ipcRenderer.invoke('disable-stealth-mode'),
    getStealthStatus: () => ipcRenderer.invoke('get-stealth-status'),
    
    // Traffic analysis protection
    enableTrafficObfuscation: () => ipcRenderer.invoke('enable-traffic-obfuscation'),
    disableTrafficObfuscation: () => ipcRenderer.invoke('disable-traffic-obfuscation'),

    // DNS over HTTPS
    enableDoH: () => ipcRenderer.invoke('enable-doh'),
    disableDoH: () => ipcRenderer.invoke('disable-doh'),

    // Steganographic features
    getSteganographicSettings: () => ipcRenderer.invoke('get-steganographic-settings'),
    updateSteganographicSettings: (settings: any) => ipcRenderer.invoke('update-steganographic-settings', settings),
    getTrafficAnalysisStats: () => ipcRenderer.invoke('get-traffic-analysis-stats'),
    getBehavioralProfile: () => ipcRenderer.invoke('get-behavioral-profile'),
    setBehavioralProfile: (profile: any) => ipcRenderer.invoke('set-behavioral-profile', profile),
    getActivityStatistics: () => ipcRenderer.invoke('get-activity-statistics'),

    // Advanced steganographic controls
    enableDecoyTraffic: () => ipcRenderer.invoke('enable-decoy-traffic'),
    disableDecoyTraffic: () => ipcRenderer.invoke('disable-decoy-traffic'),
    enableBehaviorMasking: () => ipcRenderer.invoke('enable-behavior-masking'),
    disableBehaviorMasking: () => ipcRenderer.invoke('disable-behavior-masking'),
    enableTimingRandomization: () => ipcRenderer.invoke('enable-timing-randomization'),
    disableTimingRandomization: () => ipcRenderer.invoke('disable-timing-randomization'),
    
    // Remove event listeners
    removeAllListeners: (channel: string) => ipcRenderer.removeAllListeners(channel)
});

// Inject additional privacy protection into the page
window.addEventListener('DOMContentLoaded', () => {
    // Block common tracking scripts
    const trackingScripts = [
        'google-analytics.com',
        'googletagmanager.com',
        'facebook.com/tr',
        'doubleclick.net'
    ];

    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            mutation.addedNodes.forEach((node) => {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    const element = node as Element;
                    
                    if (element.tagName === 'SCRIPT') {
                        const script = element as HTMLScriptElement;
                        const src = script.src;
                        
                        if (trackingScripts.some(tracker => src.includes(tracker))) {
                            console.log('Blocked tracking script:', src);
                            script.remove();
                        }
                    }
                    
                    // Block tracking pixels
                    if (element.tagName === 'IMG') {
                        const img = element as HTMLImageElement;
                        if (img.width === 1 && img.height === 1) {
                            console.log('Blocked tracking pixel:', img.src);
                            img.remove();
                        }
                    }
                }
            });
        });
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    // Override navigator properties for additional privacy
    Object.defineProperty(navigator, 'webdriver', {
        get: () => undefined
    });

    Object.defineProperty(navigator, 'plugins', {
        get: () => []
    });

    Object.defineProperty(navigator, 'mimeTypes', {
        get: () => []
    });

    // Block WebRTC IP leaks
    if (window.RTCPeerConnection) {
        (window as any).RTCPeerConnection = function() {
            throw new Error('WebRTC disabled for privacy');
        };
    }

    // Prevent canvas fingerprinting
    const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
    HTMLCanvasElement.prototype.toDataURL = function(...args) {
        const data = originalToDataURL.apply(this, args);
        // Add noise to canvas data
        const noise = Math.random().toString(36).substr(2, 9);
        return data.slice(0, -10) + noise + data.slice(-1);
    };

    // Prevent audio fingerprinting
    if (window.AudioContext) {
        const originalCreateAnalyser = AudioContext.prototype.createAnalyser;
        AudioContext.prototype.createAnalyser = function() {
            const analyser = originalCreateAnalyser.apply(this);
            const originalGetFloatFrequencyData = analyser.getFloatFrequencyData;

            analyser.getFloatFrequencyData = function(array: Float32Array) {
                originalGetFloatFrequencyData.apply(this, [array]);
                // Add noise to audio fingerprinting
                for (let i = 0; i < array.length; i++) {
                    if (array[i] !== undefined) {
                        array[i] += Math.random() * 0.001 - 0.0005;
                    }
                }
            };

            return analyser;
        };
    }

    console.log('Phantom Browser privacy protection loaded');
});
