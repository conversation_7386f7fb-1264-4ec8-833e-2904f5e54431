import { EventEmitter } from 'events';
export interface NetworkNode {
    id: string;
    publicKey: string;
    endpoint: string;
    lastSeen: number;
    reputation: number;
    capabilities: NodeCapabilities;
    geolocation?: {
        country: string;
        region: string;
        timezone: string;
    };
}
export interface NodeCapabilities {
    maxBandwidth: number;
    supportedProtocols: string[];
    decoyTypes: string[];
    coordinationLevel: 'basic' | 'advanced' | 'expert';
    uptime: number;
}
export interface DecoyCoordinationMessage {
    type: 'join' | 'leave' | 'heartbeat' | 'coordinate' | 'execute' | 'report';
    nodeId: string;
    timestamp: number;
    signature: string;
    payload: any;
}
export interface DecoyPattern {
    id: string;
    name: string;
    description: string;
    participants: number;
    duration: number;
    traffic: {
        type: 'http' | 'https' | 'websocket' | 'mixed';
        pattern: 'burst' | 'steady' | 'random' | 'synchronized';
        volume: number;
        targets: string[];
    };
    coordination: {
        timing: 'synchronized' | 'staggered' | 'random';
        offset: number;
        variance: number;
    };
}
export interface NetworkTopology {
    totalNodes: number;
    activeNodes: number;
    clusters: NetworkCluster[];
    averageLatency: number;
    networkHealth: number;
}
export interface NetworkCluster {
    id: string;
    nodes: string[];
    coordinator: string;
    region: string;
    averageLatency: number;
    capacity: number;
}
export declare class DistributedDecoyNetwork extends EventEmitter {
    private nodeId;
    private privateKey;
    private publicKey;
    private knownNodes;
    private activeConnections;
    private coordinationPatterns;
    private currentPattern;
    private networkTopology;
    private isCoordinator;
    private heartbeatInterval;
    private coordinationInterval;
    private blockchainCoordinator;
    private p2pNetwork;
    constructor();
    initialize(): Promise<void>;
    private generateKeyPair;
    private setupP2PHandlers;
    private handlePeerJoin;
    private handlePeerLeave;
    private handleP2PMessage;
    private handleCoordinationMessage;
    private handleBlockchainMessage;
    private handlePatternProposal;
    private handlePatternExecution;
    private handleConsensusRequest;
    private evaluatePatternParticipation;
    private evaluateConsensusVote;
    private initializeDecoyPatterns;
    private discoverBootstrapNodes;
    private joinNetwork;
    private connectToNode;
    private sendJoinMessage;
    private signMessage;
    private sendMessage;
    private evaluateCoordinatorRole;
    private startCoordinatorDuties;
    private selectAndCoordinatePattern;
    private selectOptimalPattern;
    private calculatePatternScore;
    private coordinatePattern;
    private coordinatePatternDirect;
    private selectParticipants;
    private executePattern;
    private sendDecoyRequest;
    private reportPatternCompletion;
    private findCoordinator;
    private startHeartbeat;
    private sendHeartbeat;
    private startCoordination;
    private maintainNetworkTopology;
    private identifyClusters;
    private calculateAverageLatency;
    private cleanupStaleNodes;
    getNetworkStatus(): {
        nodeId: string;
        isCoordinator: boolean;
        connectedNodes: number;
        currentPattern: string | null;
        networkHealth: number;
        totalTrafficGenerated: number;
        blockchainStatus: any;
        p2pStatus: any;
    };
    private calculateTotalTraffic;
    getNetworkTopology(): NetworkTopology | null;
    leaveNetwork(): Promise<void>;
    destroy(): void;
}
//# sourceMappingURL=DistributedDecoyNetwork.d.ts.map