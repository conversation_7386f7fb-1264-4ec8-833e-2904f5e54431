"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.P2PNetworking = void 0;
const events_1 = require("events");
const crypto = __importStar(require("crypto"));
const net = __importStar(require("net"));
class P2PNetworking extends events_1.EventEmitter {
    constructor(nodeId, privateKey, publicKey, port = 0) {
        super();
        this.peers = new Map();
        this.maxPeers = 50;
        this.isListening = false;
        this.discoveryInterval = null;
        this.maintenanceInterval = null;
        this.nodeId = nodeId;
        this.privateKey = privateKey;
        this.publicKey = publicKey;
        this.port = port;
        this.server = net.createServer();
        this.bandwidthStats = {
            bytesReceived: 0,
            bytesSent: 0,
            messagesReceived: 0,
            messagesSent: 0,
            averageLatency: 0,
            peakBandwidth: 0
        };
        this.setupServerHandlers();
    }
    async initialize() {
        await this.startListening();
        this.startPeerDiscovery();
        this.startNetworkMaintenance();
        console.log(`P2P networking initialized on port ${this.port}`);
    }
    setupServerHandlers() {
        this.server.on('connection', (socket) => {
            this.handleIncomingConnection(socket);
        });
        this.server.on('error', (error) => {
            console.error('P2P server error:', error);
            this.emit('error', error);
        });
        this.server.on('listening', () => {
            const address = this.server.address();
            this.port = address.port;
            this.isListening = true;
            console.log(`P2P server listening on port ${this.port}`);
        });
    }
    async startListening() {
        return new Promise((resolve, reject) => {
            this.server.listen(this.port, '0.0.0.0', () => {
                resolve();
            });
            this.server.on('error', reject);
        });
    }
    handleIncomingConnection(socket) {
        const remoteAddress = `${socket.remoteAddress}:${socket.remotePort}`;
        console.log(`Incoming connection from ${remoteAddress}`);
        // Create temporary peer connection
        const tempId = crypto.randomBytes(8).toString('hex');
        const peer = {
            id: tempId,
            socket,
            address: socket.remoteAddress || '',
            port: socket.remotePort || 0,
            lastSeen: Date.now(),
            latency: 0,
            bandwidth: 0,
            isInbound: true,
            handshakeComplete: false
        };
        this.setupPeerHandlers(peer);
        // Wait for handshake
        setTimeout(() => {
            if (!peer.handshakeComplete) {
                console.log(`Handshake timeout for ${remoteAddress}`);
                this.disconnectPeer(peer.id);
            }
        }, 10000); // 10 second timeout
    }
    async connectToPeer(address, port) {
        if (this.peers.size >= this.maxPeers) {
            console.log('Maximum peer connections reached');
            return false;
        }
        return new Promise((resolve) => {
            const socket = new net.Socket();
            const peerId = crypto.randomBytes(8).toString('hex');
            const peer = {
                id: peerId,
                socket,
                address,
                port,
                lastSeen: Date.now(),
                latency: 0,
                bandwidth: 0,
                isInbound: false,
                handshakeComplete: false
            };
            socket.connect(port, address, () => {
                console.log(`Connected to peer ${address}:${port}`);
                this.setupPeerHandlers(peer);
                this.sendHandshake(peer);
                resolve(true);
            });
            socket.on('error', (error) => {
                console.warn(`Failed to connect to ${address}:${port}:`, error.message);
                resolve(false);
            });
            // Connection timeout
            setTimeout(() => {
                if (!peer.handshakeComplete) {
                    socket.destroy();
                    resolve(false);
                }
            }, 10000);
        });
    }
    setupPeerHandlers(peer) {
        peer.socket.on('data', (data) => {
            this.handlePeerData(peer, data);
        });
        peer.socket.on('close', () => {
            console.log(`Peer ${peer.id} disconnected`);
            this.peers.delete(peer.id);
            this.emit('peerDisconnected', peer);
        });
        peer.socket.on('error', (error) => {
            console.warn(`Peer ${peer.id} error:`, error.message);
            this.disconnectPeer(peer.id);
        });
    }
    handlePeerData(peer, data) {
        this.bandwidthStats.bytesReceived += data.length;
        try {
            // Parse messages (assuming newline-delimited JSON)
            const messages = data.toString().trim().split('\n');
            for (const messageStr of messages) {
                if (messageStr.trim()) {
                    const message = JSON.parse(messageStr);
                    this.handlePeerMessage(peer, message);
                }
            }
        }
        catch (error) {
            console.warn(`Invalid message from peer ${peer.id}:`, error);
        }
    }
    handlePeerMessage(peer, message) {
        this.bandwidthStats.messagesReceived++;
        peer.lastSeen = Date.now();
        // Verify message signature
        if (!this.verifyMessageSignature(message)) {
            console.warn(`Invalid signature from peer ${peer.id}`);
            return;
        }
        switch (message.type) {
            case 'handshake':
                this.handleHandshake(peer, message);
                break;
            case 'ping':
                this.handlePing(peer, message);
                break;
            case 'pong':
                this.handlePong(peer, message);
                break;
            case 'data':
                this.handleDataMessage(peer, message);
                break;
            case 'discovery':
                this.handleDiscoveryMessage(peer, message);
                break;
            case 'disconnect':
                this.handleDisconnectMessage(peer, message);
                break;
        }
    }
    handleHandshake(peer, message) {
        if (peer.handshakeComplete)
            return;
        // Update peer ID with actual node ID
        this.peers.delete(peer.id);
        peer.id = message.nodeId;
        // Check if we already have this peer
        if (this.peers.has(peer.id)) {
            console.log(`Duplicate connection from ${peer.id}`);
            peer.socket.destroy();
            return;
        }
        peer.handshakeComplete = true;
        this.peers.set(peer.id, peer);
        console.log(`Handshake complete with peer ${peer.id}`);
        this.emit('peerConnected', peer);
        // Send handshake response if this was an inbound connection
        if (peer.isInbound) {
            this.sendHandshake(peer);
        }
        // Start ping/pong for latency measurement
        this.startPingPong(peer);
    }
    handlePing(peer, message) {
        // Respond with pong
        this.sendMessage(peer, {
            type: 'pong',
            nodeId: this.nodeId,
            timestamp: Date.now(),
            payload: { pingTimestamp: message.timestamp },
            signature: ''
        });
    }
    handlePong(peer, message) {
        // Calculate latency
        const latency = Date.now() - message.payload.pingTimestamp;
        peer.latency = latency;
        // Update average latency
        this.updateAverageLatency();
    }
    handleDataMessage(peer, message) {
        // Forward data message to application layer
        this.emit('dataReceived', {
            peerId: peer.id,
            data: message.payload,
            timestamp: message.timestamp
        });
    }
    handleDiscoveryMessage(peer, message) {
        // Handle peer discovery information
        const discoveredPeers = message.payload.peers || [];
        for (const peerInfo of discoveredPeers) {
            if (peerInfo.id !== this.nodeId && !this.peers.has(peerInfo.id)) {
                // Attempt to connect to discovered peer
                this.connectToPeer(peerInfo.address, peerInfo.port).catch(console.error);
            }
        }
    }
    handleDisconnectMessage(peer, message) {
        console.log(`Peer ${peer.id} is disconnecting: ${message.payload.reason}`);
        this.disconnectPeer(peer.id);
    }
    sendHandshake(peer) {
        const handshakeMessage = {
            type: 'handshake',
            nodeId: this.nodeId,
            timestamp: Date.now(),
            payload: {
                publicKey: this.publicKey.toString('hex'),
                version: '1.0.0',
                capabilities: ['decoy_coordination', 'blockchain_consensus']
            },
            signature: ''
        };
        handshakeMessage.signature = this.signMessage(handshakeMessage);
        this.sendMessage(peer, handshakeMessage);
    }
    startPingPong(peer) {
        const pingInterval = setInterval(() => {
            if (!this.peers.has(peer.id)) {
                clearInterval(pingInterval);
                return;
            }
            this.sendMessage(peer, {
                type: 'ping',
                nodeId: this.nodeId,
                timestamp: Date.now(),
                payload: {},
                signature: ''
            });
        }, 30000); // Ping every 30 seconds
    }
    sendMessage(peer, message) {
        if (!message.signature) {
            message.signature = this.signMessage(message);
        }
        const messageStr = JSON.stringify(message) + '\n';
        const messageBuffer = Buffer.from(messageStr);
        try {
            peer.socket.write(messageBuffer);
            this.bandwidthStats.bytesSent += messageBuffer.length;
            this.bandwidthStats.messagesSent++;
        }
        catch (error) {
            console.warn(`Failed to send message to peer ${peer.id}:`, error);
            this.disconnectPeer(peer.id);
        }
    }
    broadcastMessage(message) {
        message.signature = this.signMessage(message);
        for (const peer of this.peers.values()) {
            if (peer.handshakeComplete) {
                this.sendMessage(peer, message);
            }
        }
    }
    signMessage(message) {
        const messageData = JSON.stringify({
            type: message.type,
            nodeId: message.nodeId,
            timestamp: message.timestamp,
            payload: message.payload
        });
        return crypto.createHash('sha256').update(messageData + this.privateKey.toString('hex')).digest('hex');
    }
    verifyMessageSignature(message) {
        // Simplified signature verification
        // In a real implementation, this would use proper cryptographic verification
        return Boolean(message.signature && message.signature.length === 64);
    }
    disconnectPeer(peerId) {
        const peer = this.peers.get(peerId);
        if (peer) {
            peer.socket.destroy();
            this.peers.delete(peerId);
            this.emit('peerDisconnected', peer);
        }
    }
    startPeerDiscovery() {
        this.discoveryInterval = setInterval(() => {
            this.performPeerDiscovery();
        }, 60000); // Every minute
    }
    performPeerDiscovery() {
        // Request peer lists from connected peers
        const discoveryMessage = {
            type: 'discovery',
            nodeId: this.nodeId,
            timestamp: Date.now(),
            payload: {
                requestPeers: true,
                maxPeers: 10
            },
            signature: ''
        };
        this.broadcastMessage(discoveryMessage);
        // Also share our peer list
        const peerList = Array.from(this.peers.values()).map(peer => ({
            id: peer.id,
            address: peer.address,
            port: peer.port,
            latency: peer.latency
        }));
        const shareMessage = {
            type: 'discovery',
            nodeId: this.nodeId,
            timestamp: Date.now(),
            payload: {
                peers: peerList.slice(0, 10) // Share up to 10 peers
            },
            signature: ''
        };
        this.broadcastMessage(shareMessage);
    }
    startNetworkMaintenance() {
        this.maintenanceInterval = setInterval(() => {
            this.performNetworkMaintenance();
        }, 120000); // Every 2 minutes
    }
    performNetworkMaintenance() {
        const now = Date.now();
        const staleThreshold = 300000; // 5 minutes
        // Remove stale peers
        for (const [peerId, peer] of this.peers) {
            if (now - peer.lastSeen > staleThreshold) {
                console.log(`Removing stale peer: ${peerId}`);
                this.disconnectPeer(peerId);
            }
        }
        // Update bandwidth statistics
        this.updateBandwidthStats();
    }
    updateAverageLatency() {
        const latencies = Array.from(this.peers.values())
            .filter(peer => peer.latency > 0)
            .map(peer => peer.latency);
        if (latencies.length > 0) {
            this.bandwidthStats.averageLatency = latencies.reduce((a, b) => a + b, 0) / latencies.length;
        }
    }
    updateBandwidthStats() {
        // Calculate peak bandwidth (simplified)
        const totalBytes = this.bandwidthStats.bytesReceived + this.bandwidthStats.bytesSent;
        this.bandwidthStats.peakBandwidth = Math.max(this.bandwidthStats.peakBandwidth, totalBytes);
    }
    getConnectedPeers() {
        return Array.from(this.peers.values()).filter(peer => peer.handshakeComplete);
    }
    getPeerCount() {
        return this.peers.size;
    }
    getBandwidthStats() {
        return { ...this.bandwidthStats };
    }
    getNetworkInfo() {
        return {
            nodeId: this.nodeId,
            port: this.port,
            isListening: this.isListening,
            connectedPeers: this.peers.size,
            maxPeers: this.maxPeers,
            averageLatency: this.bandwidthStats.averageLatency
        };
    }
    async shutdown() {
        // Send disconnect messages to all peers
        const disconnectMessage = {
            type: 'disconnect',
            nodeId: this.nodeId,
            timestamp: Date.now(),
            payload: { reason: 'shutdown' },
            signature: ''
        };
        this.broadcastMessage(disconnectMessage);
        // Close all peer connections
        for (const peer of this.peers.values()) {
            peer.socket.destroy();
        }
        this.peers.clear();
        // Stop intervals
        if (this.discoveryInterval) {
            clearInterval(this.discoveryInterval);
            this.discoveryInterval = null;
        }
        if (this.maintenanceInterval) {
            clearInterval(this.maintenanceInterval);
            this.maintenanceInterval = null;
        }
        // Close server
        return new Promise((resolve) => {
            this.server.close(() => {
                this.isListening = false;
                console.log('P2P networking shutdown complete');
                resolve();
            });
        });
    }
}
exports.P2PNetworking = P2PNetworking;
//# sourceMappingURL=P2PNetworking.js.map