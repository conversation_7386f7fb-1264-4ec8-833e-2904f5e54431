"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
// Expose secure API to renderer process
electron_1.contextBridge.exposeInMainWorld('phantomAPI', {
    // Navigation
    navigate: (url) => electron_1.ipcRenderer.invoke('navigate', url),
    goBack: () => electron_1.ipcRenderer.invoke('go-back'),
    goForward: () => electron_1.ipcRenderer.invoke('go-forward'),
    reload: () => electron_1.ipcRenderer.invoke('reload'),
    // Tab management
    createTab: (url) => electron_1.ipcRenderer.invoke('create-tab', url),
    closeTab: (tabId) => electron_1.ipcRenderer.invoke('close-tab', tabId),
    switchTab: (tabId) => electron_1.ipcRenderer.invoke('switch-tab', tabId),
    getTabs: () => electron_1.ipcRenderer.invoke('get-tabs'),
    // Privacy settings
    getPrivacySettings: () => electron_1.ipcRenderer.invoke('get-privacy-settings'),
    updatePrivacySettings: (settings) => electron_1.ipcRenderer.invoke('update-privacy-settings', settings),
    clearBrowsingData: () => electron_1.ipcRenderer.invoke('clear-browsing-data'),
    // Fingerprint protection
    getFingerprintProfile: () => electron_1.ipcRenderer.invoke('get-fingerprint-profile'),
    setFingerprintProfile: (profile) => electron_1.ipcRenderer.invoke('set-fingerprint-profile', profile),
    rotateFingerprintProfile: () => electron_1.ipcRenderer.invoke('rotate-fingerprint-profile'),
    // User agent management
    getUserAgentProfile: () => electron_1.ipcRenderer.invoke('get-user-agent-profile'),
    setUserAgentProfile: (profile) => electron_1.ipcRenderer.invoke('set-user-agent-profile', profile),
    rotateUserAgent: () => electron_1.ipcRenderer.invoke('rotate-user-agent'),
    enableUserAgentRotation: () => electron_1.ipcRenderer.invoke('enable-user-agent-rotation'),
    disableUserAgentRotation: () => electron_1.ipcRenderer.invoke('disable-user-agent-rotation'),
    // Proxy management
    getProxySettings: () => electron_1.ipcRenderer.invoke('get-proxy-settings'),
    setProxy: (config) => electron_1.ipcRenderer.invoke('set-proxy', config),
    clearProxy: () => electron_1.ipcRenderer.invoke('clear-proxy'),
    testProxy: (config) => electron_1.ipcRenderer.invoke('test-proxy', config),
    enableProxyRotation: (interval) => electron_1.ipcRenderer.invoke('enable-proxy-rotation', interval),
    disableProxyRotation: () => electron_1.ipcRenderer.invoke('disable-proxy-rotation'),
    // Security
    getSecuritySettings: () => electron_1.ipcRenderer.invoke('get-security-settings'),
    updateSecuritySettings: (settings) => electron_1.ipcRenderer.invoke('update-security-settings', settings),
    performSecurityAudit: () => electron_1.ipcRenderer.invoke('perform-security-audit'),
    // VPN
    connectVPN: (config) => electron_1.ipcRenderer.invoke('connect-vpn', config),
    disconnectVPN: () => electron_1.ipcRenderer.invoke('disconnect-vpn'),
    getVPNStatus: () => electron_1.ipcRenderer.invoke('get-vpn-status'),
    // Events
    onTabCreated: (callback) => {
        electron_1.ipcRenderer.on('tab-created', (event, tab) => callback(tab));
    },
    onTabClosed: (callback) => {
        electron_1.ipcRenderer.on('tab-closed', (event, tabId) => callback(tabId));
    },
    onTabUpdated: (callback) => {
        electron_1.ipcRenderer.on('tab-updated', (event, tab) => callback(tab));
    },
    onNavigationComplete: (callback) => {
        electron_1.ipcRenderer.on('navigation-complete', (event, data) => callback(data));
    },
    onPrivacyAlert: (callback) => {
        electron_1.ipcRenderer.on('privacy-alert', (event, alert) => callback(alert));
    },
    onNavigateWebview: (callback) => {
        electron_1.ipcRenderer.on('navigate-webview', (event, url) => callback(url));
    },
    // Utility
    openDevTools: () => electron_1.ipcRenderer.invoke('open-dev-tools'),
    closeDevTools: () => electron_1.ipcRenderer.invoke('close-dev-tools'),
    takeScreenshot: () => electron_1.ipcRenderer.invoke('take-screenshot'),
    exportSettings: () => electron_1.ipcRenderer.invoke('export-settings'),
    importSettings: (settings) => electron_1.ipcRenderer.invoke('import-settings', settings),
    // Advanced features
    enableStealthMode: () => electron_1.ipcRenderer.invoke('enable-stealth-mode'),
    disableStealthMode: () => electron_1.ipcRenderer.invoke('disable-stealth-mode'),
    getStealthStatus: () => electron_1.ipcRenderer.invoke('get-stealth-status'),
    // Traffic analysis protection
    enableTrafficObfuscation: () => electron_1.ipcRenderer.invoke('enable-traffic-obfuscation'),
    disableTrafficObfuscation: () => electron_1.ipcRenderer.invoke('disable-traffic-obfuscation'),
    // DNS over HTTPS
    enableDoH: () => electron_1.ipcRenderer.invoke('enable-doh'),
    disableDoH: () => electron_1.ipcRenderer.invoke('disable-doh'),
    // Steganographic features
    getSteganographicSettings: () => electron_1.ipcRenderer.invoke('get-steganographic-settings'),
    updateSteganographicSettings: (settings) => electron_1.ipcRenderer.invoke('update-steganographic-settings', settings),
    getTrafficAnalysisStats: () => electron_1.ipcRenderer.invoke('get-traffic-analysis-stats'),
    getBehavioralProfile: () => electron_1.ipcRenderer.invoke('get-behavioral-profile'),
    setBehavioralProfile: (profile) => electron_1.ipcRenderer.invoke('set-behavioral-profile', profile),
    getActivityStatistics: () => electron_1.ipcRenderer.invoke('get-activity-statistics'),
    // Advanced steganographic controls
    enableDecoyTraffic: () => electron_1.ipcRenderer.invoke('enable-decoy-traffic'),
    disableDecoyTraffic: () => electron_1.ipcRenderer.invoke('disable-decoy-traffic'),
    enableBehaviorMasking: () => electron_1.ipcRenderer.invoke('enable-behavior-masking'),
    disableBehaviorMasking: () => electron_1.ipcRenderer.invoke('disable-behavior-masking'),
    enableTimingRandomization: () => electron_1.ipcRenderer.invoke('enable-timing-randomization'),
    disableTimingRandomization: () => electron_1.ipcRenderer.invoke('disable-timing-randomization'),
    // Remove event listeners
    removeAllListeners: (channel) => electron_1.ipcRenderer.removeAllListeners(channel)
});
// Inject additional privacy protection into the page
window.addEventListener('DOMContentLoaded', () => {
    // Block common tracking scripts
    const trackingScripts = [
        'google-analytics.com',
        'googletagmanager.com',
        'facebook.com/tr',
        'doubleclick.net'
    ];
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            mutation.addedNodes.forEach((node) => {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    const element = node;
                    if (element.tagName === 'SCRIPT') {
                        const script = element;
                        const src = script.src;
                        if (trackingScripts.some(tracker => src.includes(tracker))) {
                            console.log('Blocked tracking script:', src);
                            script.remove();
                        }
                    }
                    // Block tracking pixels
                    if (element.tagName === 'IMG') {
                        const img = element;
                        if (img.width === 1 && img.height === 1) {
                            console.log('Blocked tracking pixel:', img.src);
                            img.remove();
                        }
                    }
                }
            });
        });
    });
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
    // Override navigator properties for additional privacy
    Object.defineProperty(navigator, 'webdriver', {
        get: () => undefined
    });
    Object.defineProperty(navigator, 'plugins', {
        get: () => []
    });
    Object.defineProperty(navigator, 'mimeTypes', {
        get: () => []
    });
    // Block WebRTC IP leaks
    if (window.RTCPeerConnection) {
        window.RTCPeerConnection = function () {
            throw new Error('WebRTC disabled for privacy');
        };
    }
    // Prevent canvas fingerprinting
    const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
    HTMLCanvasElement.prototype.toDataURL = function (...args) {
        const data = originalToDataURL.apply(this, args);
        // Add noise to canvas data
        const noise = Math.random().toString(36).substr(2, 9);
        return data.slice(0, -10) + noise + data.slice(-1);
    };
    // Prevent audio fingerprinting
    if (window.AudioContext) {
        const originalCreateAnalyser = AudioContext.prototype.createAnalyser;
        AudioContext.prototype.createAnalyser = function () {
            const analyser = originalCreateAnalyser.apply(this);
            const originalGetFloatFrequencyData = analyser.getFloatFrequencyData;
            analyser.getFloatFrequencyData = function (array) {
                originalGetFloatFrequencyData.apply(this, [array]);
                // Add noise to audio fingerprinting
                for (let i = 0; i < array.length; i++) {
                    if (array[i] !== undefined) {
                        array[i] += Math.random() * 0.001 - 0.0005;
                    }
                }
            };
            return analyser;
        };
    }
    console.log('Phantom Browser privacy protection loaded');
});
//# sourceMappingURL=preload.js.map