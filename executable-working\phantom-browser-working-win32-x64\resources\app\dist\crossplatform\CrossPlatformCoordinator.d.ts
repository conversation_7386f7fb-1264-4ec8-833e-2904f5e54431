import { EventEmitter } from 'events';
export interface DeviceProfile {
    id: string;
    type: 'desktop' | 'mobile' | 'tablet' | 'iot' | 'smart_tv' | 'wearable';
    platform: 'windows' | 'macos' | 'linux' | 'android' | 'ios' | 'embedded';
    capabilities: DeviceCapabilities;
    location: DeviceLocation;
    networkInfo: NetworkInfo;
    lastSeen: number;
    trustLevel: number;
    coordinationRole: 'coordinator' | 'participant' | 'observer';
}
export interface DeviceCapabilities {
    steganographicFeatures: string[];
    maxBandwidth: number;
    batteryLevel?: number;
    processingPower: 'low' | 'medium' | 'high' | 'very_high';
    storageCapacity: number;
    networkTypes: ('wifi' | 'cellular' | 'ethernet' | 'bluetooth')[];
    sensors: string[];
    displayCapabilities?: {
        resolution: {
            width: number;
            height: number;
        };
        colorDepth: number;
        refreshRate: number;
    };
}
export interface DeviceLocation {
    timezone: string;
    country?: string;
    region?: string;
    coordinates?: {
        lat: number;
        lon: number;
    };
    networkLocation: 'home' | 'work' | 'public' | 'mobile' | 'unknown';
}
export interface NetworkInfo {
    ipAddress: string;
    networkType: 'wifi' | 'cellular' | 'ethernet' | 'vpn';
    connectionQuality: 'poor' | 'fair' | 'good' | 'excellent';
    latency: number;
    bandwidth: {
        upload: number;
        download: number;
    };
    isMetered: boolean;
}
export interface CoordinationPattern {
    id: string;
    name: string;
    description: string;
    targetDevices: string[];
    minParticipants: number;
    maxParticipants: number;
    duration: number;
    coordination: {
        timing: 'synchronized' | 'staggered' | 'random' | 'adaptive';
        synchronizationTolerance: number;
        adaptationRate: number;
    };
    activities: CoordinationActivity[];
    requirements: {
        minBandwidth: number;
        minBatteryLevel?: number;
        requiredCapabilities: string[];
        networkTypes: string[];
    };
}
export interface CoordinationActivity {
    type: 'web_browsing' | 'app_usage' | 'network_traffic' | 'sensor_data' | 'display_activity';
    platform: string;
    parameters: {
        frequency: number;
        variance: number;
        intensity: 'low' | 'medium' | 'high';
        targets?: string[];
    };
    steganographicLayer: {
        obfuscationType: string;
        coverTraffic: boolean;
        timingRandomization: boolean;
        patternMasking: boolean;
    };
}
export interface CrossPlatformSession {
    id: string;
    coordinator: string;
    participants: Map<string, DeviceProfile>;
    pattern: CoordinationPattern;
    status: 'initializing' | 'coordinating' | 'executing' | 'completed' | 'failed';
    startTime: number;
    endTime?: number;
    synchronizationData: {
        baseTimestamp: number;
        offsetMap: Map<string, number>;
        qualityMetrics: Map<string, number>;
    };
    metrics: {
        participationRate: number;
        synchronizationAccuracy: number;
        coverageArea: number;
        anonymitySet: number;
    };
}
export declare class CrossPlatformCoordinator extends EventEmitter {
    private deviceId;
    private deviceProfile;
    private knownDevices;
    private coordinationPatterns;
    private activeSessions;
    private cloudCoordinator;
    private isCoordinator;
    private discoveryInterval;
    private synchronizationInterval;
    constructor();
    initialize(): Promise<void>;
    private generateDeviceId;
    private getDeviceFingerprint;
    private detectDeviceProfile;
    private detectCapabilities;
    private detectLocation;
    private detectNetworkInfo;
    private initializeCoordinationPatterns;
    private registerWithNetwork;
    private evaluateCoordinatorRole;
    private startDeviceDiscovery;
    private discoverDevices;
    private startSynchronizationService;
    private performTimeSynchronization;
    startCoordinatedSession(patternId: string, targetDevices?: string[]): Promise<string>;
    private selectParticipants;
    private isDeviceCompatible;
    private coordinateSession;
    private waitForSynchronization;
    private executeCoordinatedActivities;
    private executeActivity;
    private performActivityAction;
    private performWebBrowsingAction;
    private performNetworkTrafficAction;
    private performAppUsageAction;
    private updateSessionMetrics;
    getCoordinationStatus(): {
        deviceProfile: DeviceProfile;
        knownDevices: number;
        activeSessions: number;
        isCoordinator: boolean;
        availablePatterns: string[];
    };
    getSessionStatus(sessionId: string): CrossPlatformSession | null;
    destroy(): Promise<void>;
}
//# sourceMappingURL=CrossPlatformCoordinator.d.ts.map