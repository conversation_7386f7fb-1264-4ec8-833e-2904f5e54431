// Phantom Browser Renderer Script

// Search Engine Classes
class SearchEngine {
    constructor() {
        this.providers = new Map();
        this.settings = {
            defaultProvider: 'duckduckgo',
            enableSuggestions: true,
            enableHistory: true,
            maxSuggestions: 8,
            privacyMode: 'balanced',
            customProviders: []
        };
        this.searchHistory = [];
        this.maxHistorySize = 1000;
        this.initializeDefaultProviders();
    }

    initializeDefaultProviders() {
        const defaultProviders = [
            {
                id: 'duckduckgo',
                name: 'DuckDuckGo',
                baseUrl: 'https://duckduckgo.com',
                searchUrl: 'https://duckduckgo.com/?q={query}&t=phantom',
                privacyRating: 10,
                features: ['No tracking', 'No ads', 'Tor support', 'Instant answers'],
                description: 'Privacy-focused search engine with no tracking'
            },
            {
                id: 'startpage',
                name: 'Startpage',
                baseUrl: 'https://www.startpage.com',
                searchUrl: 'https://www.startpage.com/sp/search?query={query}&cat=web&pl=phantom',
                privacyRating: 9,
                features: ['Google results', 'No tracking', 'Anonymous view', 'EU based'],
                description: 'Private search with Google results, no tracking'
            },
            {
                id: 'brave',
                name: 'Brave Search',
                baseUrl: 'https://search.brave.com',
                searchUrl: 'https://search.brave.com/search?q={query}&source=phantom',
                privacyRating: 8,
                features: ['Independent index', 'No tracking', 'Ad-free', 'Fast results'],
                description: 'Independent search engine by Brave'
            },
            {
                id: 'searx',
                name: 'SearX',
                baseUrl: 'https://searx.org',
                searchUrl: 'https://searx.org/search?q={query}&categories=general',
                privacyRating: 10,
                features: ['Open source', 'No tracking', 'Aggregated results', 'Self-hostable'],
                description: 'Open source metasearch engine'
            }
        ];

        defaultProviders.forEach(provider => {
            this.providers.set(provider.id, provider);
        });
    }

    getProviders() {
        return Array.from(this.providers.values());
    }

    getProvider(id) {
        return this.providers.get(id);
    }

    getDefaultProvider() {
        return this.providers.get(this.settings.defaultProvider) || this.providers.get('duckduckgo');
    }

    setDefaultProvider(providerId) {
        if (this.providers.has(providerId)) {
            this.settings.defaultProvider = providerId;
        }
    }

    search(query, providerId) {
        const provider = providerId ? this.providers.get(providerId) : this.getDefaultProvider();
        if (!provider) {
            throw new Error('Search provider not found');
        }

        this.addToHistory(query, provider.id);
        const searchUrl = provider.searchUrl.replace('{query}', encodeURIComponent(query));
        console.log(`Searching "${query}" with ${provider.name}: ${searchUrl}`);
        return searchUrl;
    }

    async getSuggestions(query) {
        if (!this.settings.enableSuggestions || query.length < 2) {
            return [];
        }

        const suggestions = [];

        // Add history suggestions
        if (this.settings.enableHistory) {
            const historySuggestions = this.getHistorySuggestions(query);
            suggestions.push(...historySuggestions);
        }

        // Add mock suggestions
        const mockSuggestions = [
            `${query} tutorial`,
            `${query} guide`,
            `${query} examples`
        ];

        mockSuggestions.forEach(suggestion => {
            suggestions.push({
                query: suggestion,
                type: 'suggestion'
            });
        });

        return suggestions.slice(0, this.settings.maxSuggestions);
    }

    getHistorySuggestions(query) {
        const lowerQuery = query.toLowerCase();
        return this.searchHistory
            .filter(item => item.query.toLowerCase().includes(lowerQuery))
            .slice(0, 3)
            .map(item => ({
                query: item.query,
                type: 'history'
            }));
    }

    addToHistory(query, provider) {
        this.searchHistory = this.searchHistory.filter(item => item.query !== query);
        this.searchHistory.unshift({
            query,
            timestamp: Date.now(),
            provider
        });

        if (this.searchHistory.length > this.maxHistorySize) {
            this.searchHistory = this.searchHistory.slice(0, this.maxHistorySize);
        }
    }

    isValidUrl(input) {
        try {
            new URL(input);
            return true;
        } catch {
            const domainPattern = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/;
            return domainPattern.test(input) || input.includes('.');
        }
    }

    processInput(input) {
        const trimmedInput = input.trim();

        if (this.isValidUrl(trimmedInput)) {
            const url = trimmedInput.startsWith('http') ? trimmedInput : `https://${trimmedInput}`;
            return { type: 'url', value: url };
        } else {
            const searchUrl = this.search(trimmedInput);
            return { type: 'search', value: searchUrl };
        }
    }

    getSettings() {
        return { ...this.settings };
    }

    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
    }
}

class PhantomBrowserUI {
    constructor() {
        this.currentTabId = 'default';
        this.tabs = new Map();
        this.privacyStats = {
            blockedTrackers: 0,
            blockedAds: 0,
            fingerprintAttempts: 0,
            dataLeaks: 0,
            decoyRequests: 0,
            obfuscatedPackets: 0
        };
        this.searchEngine = null;
        this.searchUI = null;

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupPrivacyPanel();
        this.loadInitialSettings();
        this.startStatsUpdater();
        this.setupIpcListeners();
        this.initializeSearchEngine();
    }

    setupEventListeners() {
        // Navigation buttons
        document.getElementById('backBtn').addEventListener('click', () => {
            window.phantomAPI?.goBack();
        });

        document.getElementById('forwardBtn').addEventListener('click', () => {
            window.phantomAPI?.goForward();
        });

        document.getElementById('reloadBtn').addEventListener('click', () => {
            window.phantomAPI?.reload();
        });

        // Address bar
        const addressBar = document.getElementById('addressBar');
        if (addressBar) {
            addressBar.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    console.log('Search triggered:', addressBar.value);
                    this.navigate(addressBar.value);
                }
            });
            console.log('Address bar event listener attached successfully');
        } else {
            console.error('Address bar element not found!');
        }

        // New tab button
        document.getElementById('newTabBtn').addEventListener('click', () => {
            this.createNewTab();
        });

        // Privacy panel toggle
        document.getElementById('privacyPanelBtn').addEventListener('click', () => {
            this.togglePrivacyPanel();
        });

        // User agent rotation
        document.getElementById('rotateUserAgentBtn').addEventListener('click', () => {
            this.rotateUserAgent();
        });

        // Privacy toggles
        document.querySelectorAll('.toggle-switch').forEach(toggle => {
            toggle.addEventListener('click', () => {
                this.togglePrivacySetting(toggle);
            });
        });

        // Obfuscation intensity selector
        const obfuscationIntensity = document.getElementById('obfuscationIntensity');
        if (obfuscationIntensity) {
            obfuscationIntensity.addEventListener('change', (e) => {
                this.updateObfuscationIntensity(e.target.value);
            });
        }

        // Webview events
        const webview = document.getElementById('webview');
        webview.addEventListener('did-start-loading', () => {
            this.showLoading();
        });

        webview.addEventListener('did-stop-loading', () => {
            this.hideLoading();
        });

        webview.addEventListener('did-navigate', (e) => {
            this.updateAddressBar(e.url);
            this.updatePrivacyIndicator(e.url);
        });

        webview.addEventListener('page-title-updated', (e) => {
            this.updateTabTitle(e.title);
        });
    }

    setupPrivacyPanel() {
        // Initialize privacy panel state
        this.privacyPanelOpen = false;

        // Load privacy settings from main process
        this.loadPrivacySettings();
    }

    setupIpcListeners() {
        // Listen for navigation commands from main process
        if (window.phantomAPI && window.phantomAPI.onNavigateWebview) {
            window.phantomAPI.onNavigateWebview((url) => {
                console.log('Received navigation command for URL:', url);
                const webview = document.getElementById('webview');
                if (webview) {
                    webview.src = url;
                    console.log('Webview src set to:', url);
                } else {
                    console.error('Webview element not found');
                }
            });
        } else {
            // Fallback: use electron's ipcRenderer directly if available
            if (window.require) {
                try {
                    const { ipcRenderer } = window.require('electron');
                    ipcRenderer.on('navigate-webview', (event, url) => {
                        console.log('Received navigation command for URL:', url);
                        const webview = document.getElementById('webview');
                        if (webview) {
                            webview.src = url;
                            console.log('Webview src set to:', url);
                        } else {
                            console.error('Webview element not found');
                        }
                    });
                } catch (error) {
                    console.warn('Could not setup IPC listener:', error);
                }
            }
        }
    }

    initializeSearchEngine() {
        this.searchEngine = new SearchEngine();
        this.setupSearchUI();
        console.log('Search engine initialized');
    }

    setupSearchUI() {
        // Create search suggestions container
        const addressBar = document.getElementById('addressBar');
        if (!addressBar) return;

        // Wrap address bar in container for positioning
        const container = document.createElement('div');
        container.className = 'address-bar-container';
        addressBar.parentNode.insertBefore(container, addressBar);
        container.appendChild(addressBar);

        // Create suggestions dropdown
        const suggestionsContainer = document.createElement('div');
        suggestionsContainer.id = 'searchSuggestions';
        suggestionsContainer.className = 'search-suggestions hidden';
        suggestionsContainer.innerHTML = '<div class="suggestions-list"></div>';
        container.appendChild(suggestionsContainer);

        // Add search provider section to privacy panel
        this.addSearchProviderSection();

        // Setup search event listeners
        this.setupSearchEventListeners();
    }

    addSearchProviderSection() {
        const privacyPanel = document.querySelector('.privacy-panel');
        if (!privacyPanel) return;

        const searchSection = document.createElement('div');
        searchSection.className = 'privacy-section';
        searchSection.innerHTML = `
            <h3>Search Engine</h3>
            <div class="search-provider-selector">
                <label for="searchProvider">Default Search Provider:</label>
                <select id="searchProvider" class="provider-select">
                    ${this.generateProviderOptions()}
                </select>
            </div>
            <div class="search-settings">
                <div class="setting-item">
                    <label class="toggle-switch active" data-setting="searchSuggestions">
                        <span class="toggle-slider"></span>
                    </label>
                    <span class="setting-label">Search Suggestions</span>
                </div>
                <div class="setting-item">
                    <label class="toggle-switch active" data-setting="searchHistory">
                        <span class="toggle-slider"></span>
                    </label>
                    <span class="setting-label">Search History</span>
                </div>
            </div>
            <div class="provider-info">
                <div id="providerDetails" class="provider-details"></div>
            </div>
        `;

        privacyPanel.appendChild(searchSection);
        this.updateProviderInfo();
    }

    generateProviderOptions() {
        const providers = this.searchEngine.getProviders();
        const defaultProvider = this.searchEngine.getDefaultProvider();

        return providers.map(provider =>
            `<option value="${provider.id}" ${provider.id === defaultProvider.id ? 'selected' : ''}>
                ${provider.name} (Privacy: ${provider.privacyRating}/10)
            </option>`
        ).join('');
    }

    setupSearchEventListeners() {
        const addressBar = document.getElementById('addressBar');
        if (!addressBar) return;

        let debounceTimer = null;
        let currentSuggestionIndex = -1;
        let suggestions = [];

        // Input event for suggestions
        addressBar.addEventListener('input', async (e) => {
            const value = e.target.value;

            if (debounceTimer) {
                clearTimeout(debounceTimer);
            }

            debounceTimer = setTimeout(async () => {
                if (value.length >= 2) {
                    suggestions = await this.searchEngine.getSuggestions(value);
                    this.renderSuggestions(suggestions);
                    this.showSuggestions();
                } else {
                    this.hideSuggestions();
                }
            }, 300);
        });

        // Keydown for navigation
        addressBar.addEventListener('keydown', (e) => {
            const suggestionsContainer = document.getElementById('searchSuggestions');
            if (!suggestionsContainer || suggestionsContainer.classList.contains('hidden')) {
                return;
            }

            switch (e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    currentSuggestionIndex = this.navigateSuggestions(suggestions, currentSuggestionIndex, 1);
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    currentSuggestionIndex = this.navigateSuggestions(suggestions, currentSuggestionIndex, -1);
                    break;
                case 'Enter':
                    e.preventDefault();
                    if (currentSuggestionIndex >= 0 && suggestions[currentSuggestionIndex]) {
                        this.performSearch(suggestions[currentSuggestionIndex].query);
                    } else {
                        this.performSearch(addressBar.value);
                    }
                    this.hideSuggestions();
                    currentSuggestionIndex = -1;
                    break;
                case 'Escape':
                    this.hideSuggestions();
                    currentSuggestionIndex = -1;
                    break;
            }
        });

        // Focus/blur events
        addressBar.addEventListener('focus', () => {
            if (suggestions.length > 0) {
                this.showSuggestions();
            }
        });

        addressBar.addEventListener('blur', () => {
            setTimeout(() => this.hideSuggestions(), 150);
        });

        // Provider selector
        const providerSelect = document.getElementById('searchProvider');
        if (providerSelect) {
            providerSelect.addEventListener('change', (e) => {
                this.searchEngine.setDefaultProvider(e.target.value);
                this.updateProviderInfo();
            });
        }
    }

    async loadInitialSettings() {
        try {
            if (window.phantomAPI) {
                const privacySettings = await window.phantomAPI.getPrivacySettings();
                const userAgentProfile = await window.phantomAPI.getUserAgentProfile();
                const proxySettings = await window.phantomAPI.getProxySettings();
                const steganographicSettings = await window.phantomAPI.getSteganographicSettings();

                this.updateUIFromSettings(privacySettings, userAgentProfile, proxySettings, steganographicSettings);
            }
        } catch (error) {
            console.error('Failed to load initial settings:', error);
        }
    }

    updateUIFromSettings(privacySettings, userAgentProfile, proxySettings, steganographicSettings) {
        // Update toggle switches based on settings
        const toggles = {
            'canvasProtection': privacySettings.randomizeCanvasFingerprint,
            'webglProtection': privacySettings.blockFingerprinting,
            'audioProtection': privacySettings.blockFingerprinting,
            'blockTrackers': privacySettings.blockTrackers,
            'blockAds': privacySettings.blockAds,
            'webrtcProtection': privacySettings.blockWebRTC,
            'userAgentRotation': true, // Assume enabled
            'proxyEnabled': proxySettings?.enabled || false,
            'proxyRotation': false, // Assume disabled initially
            'trafficObfuscation': steganographicSettings?.enableTrafficObfuscation || true,
            'timingRandomization': steganographicSettings?.enableTimingRandomization || true,
            'behaviorMasking': steganographicSettings?.enableBehaviorMasking || true,
            'decoyTraffic': steganographicSettings?.enableDecoyTraffic || true,
            'requestPadding': steganographicSettings?.enableRequestPadding || true,
            'dnsObfuscation': steganographicSettings?.enableDNSObfuscation || true
        };

        Object.entries(toggles).forEach(([setting, enabled]) => {
            const toggle = document.querySelector(`[data-setting="${setting}"]`);
            if (toggle) {
                toggle.classList.toggle('active', enabled);
            }
        });

        // Update obfuscation intensity selector
        const intensitySelector = document.getElementById('obfuscationIntensity');
        if (intensitySelector && steganographicSettings?.obfuscationIntensity) {
            intensitySelector.value = steganographicSettings.obfuscationIntensity;
        }
    }

    navigate(url) {
        if (!url) return;

        // Use search engine to process input
        if (this.searchEngine) {
            const result = this.searchEngine.processInput(url);
            url = result.value;
        } else {
            // Fallback to old logic
            if (!url.startsWith('http://') && !url.startsWith('https://') && !url.startsWith('file://')) {
                if (url.includes('.') && !url.includes(' ')) {
                    url = 'https://' + url;
                } else {
                    url = `https://duckduckgo.com/?q=${encodeURIComponent(url)}`;
                }
            }
        }

        const webview = document.getElementById('webview');
        webview.src = url;

        if (window.phantomAPI) {
            window.phantomAPI.navigate(url);
        }
    }

    performSearch(query) {
        if (this.searchEngine) {
            const result = this.searchEngine.processInput(query);
            const addressBar = document.getElementById('addressBar');
            if (addressBar) {
                addressBar.value = result.type === 'url' ? result.value : query;
            }
            this.navigate(result.value);
        } else {
            this.navigate(query);
        }
    }

    renderSuggestions(suggestions) {
        const suggestionsContainer = document.getElementById('searchSuggestions');
        if (!suggestionsContainer) return;

        const suggestionsList = suggestionsContainer.querySelector('.suggestions-list');
        if (!suggestionsList) return;

        suggestionsList.innerHTML = suggestions.map((suggestion, index) => `
            <div class="suggestion-item" data-index="${index}">
                <div class="suggestion-icon">
                    ${this.getSuggestionIcon(suggestion.type)}
                </div>
                <div class="suggestion-text">
                    <span class="suggestion-query">${this.escapeHtml(suggestion.query)}</span>
                    ${suggestion.title ? `<span class="suggestion-title">${this.escapeHtml(suggestion.title)}</span>` : ''}
                </div>
                <div class="suggestion-type">${suggestion.type}</div>
            </div>
        `).join('');

        // Add click listeners
        suggestionsList.querySelectorAll('.suggestion-item').forEach((item, index) => {
            item.addEventListener('click', () => {
                this.performSearch(suggestions[index].query);
                this.hideSuggestions();
            });
        });
    }

    getSuggestionIcon(type) {
        switch (type) {
            case 'history':
                return '🕒';
            case 'bookmark':
                return '⭐';
            case 'suggestion':
            default:
                return '🔍';
        }
    }

    navigateSuggestions(suggestions, currentIndex, direction) {
        const suggestionElements = document.querySelectorAll('.suggestion-item');
        if (!suggestionElements || suggestionElements.length === 0) return currentIndex;

        // Remove current highlight
        if (currentIndex >= 0) {
            suggestionElements[currentIndex].classList.remove('highlighted');
        }

        // Calculate new index
        let newIndex = currentIndex + direction;
        if (newIndex < 0) {
            newIndex = suggestionElements.length - 1;
        } else if (newIndex >= suggestionElements.length) {
            newIndex = 0;
        }

        // Highlight new suggestion
        suggestionElements[newIndex].classList.add('highlighted');

        // Update address bar with suggestion
        const addressBar = document.getElementById('addressBar');
        if (addressBar && suggestions[newIndex]) {
            addressBar.value = suggestions[newIndex].query;
        }

        return newIndex;
    }

    showSuggestions() {
        const suggestionsContainer = document.getElementById('searchSuggestions');
        if (suggestionsContainer) {
            suggestionsContainer.classList.remove('hidden');
        }
    }

    hideSuggestions() {
        const suggestionsContainer = document.getElementById('searchSuggestions');
        if (suggestionsContainer) {
            suggestionsContainer.classList.add('hidden');
        }
    }

    updateProviderInfo() {
        if (!this.searchEngine) return;

        const provider = this.searchEngine.getDefaultProvider();
        const detailsElement = document.getElementById('providerDetails');

        if (detailsElement) {
            detailsElement.innerHTML = `
                <div class="provider-card">
                    <h4>${provider.name}</h4>
                    <p class="provider-description">${provider.description}</p>
                    <div class="provider-rating">
                        Privacy Rating: <span class="rating-value">${provider.privacyRating}/10</span>
                        <div class="rating-bar">
                            <div class="rating-fill" style="width: ${provider.privacyRating * 10}%"></div>
                        </div>
                    </div>
                    <div class="provider-features">
                        <strong>Features:</strong>
                        <ul>
                            ${provider.features.map(feature => `<li>${feature}</li>`).join('')}
                        </ul>
                    </div>
                </div>
            `;
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    createNewTab() {
        const tabId = 'tab-' + Date.now();
        this.tabs.set(tabId, {
            id: tabId,
            title: 'New Tab',
            url: 'about:blank'
        });

        // Create tab element
        const tabBar = document.querySelector('.tab-bar');
        const newTabBtn = document.getElementById('newTabBtn');
        
        const tabElement = document.createElement('div');
        tabElement.className = 'tab';
        tabElement.dataset.tabId = tabId;
        tabElement.innerHTML = `
            <div class="tab-title">New Tab</div>
            <button class="tab-close">×</button>
        `;

        tabBar.insertBefore(tabElement, newTabBtn);

        // Add event listeners
        tabElement.addEventListener('click', (e) => {
            if (!e.target.classList.contains('tab-close')) {
                this.switchTab(tabId);
            }
        });

        tabElement.querySelector('.tab-close').addEventListener('click', (e) => {
            e.stopPropagation();
            this.closeTab(tabId);
        });

        this.switchTab(tabId);
    }

    switchTab(tabId) {
        // Update active tab
        document.querySelectorAll('.tab').forEach(tab => {
            tab.classList.remove('active');
        });
        
        const tabElement = document.querySelector(`[data-tab-id="${tabId}"]`);
        if (tabElement) {
            tabElement.classList.add('active');
        }

        this.currentTabId = tabId;
        
        // Update webview content
        const tab = this.tabs.get(tabId);
        if (tab && tab.url !== 'about:blank') {
            const webview = document.getElementById('webview');
            webview.src = tab.url;
        }
    }

    closeTab(tabId) {
        const tabElement = document.querySelector(`[data-tab-id="${tabId}"]`);
        if (tabElement) {
            tabElement.remove();
        }

        this.tabs.delete(tabId);

        // Switch to another tab if current tab was closed
        if (this.currentTabId === tabId) {
            const remainingTabs = document.querySelectorAll('.tab');
            if (remainingTabs.length > 0) {
                const firstTab = remainingTabs[0];
                this.switchTab(firstTab.dataset.tabId);
            }
        }
    }

    updateTabTitle(title) {
        const tabElement = document.querySelector(`[data-tab-id="${this.currentTabId}"] .tab-title`);
        if (tabElement) {
            tabElement.textContent = title || 'New Tab';
        }

        const tab = this.tabs.get(this.currentTabId);
        if (tab) {
            tab.title = title;
        }
    }

    updateAddressBar(url) {
        const addressBar = document.getElementById('addressBar');
        addressBar.value = url;

        const tab = this.tabs.get(this.currentTabId);
        if (tab) {
            tab.url = url;
        }
    }

    updatePrivacyIndicator(url) {
        const indicator = document.getElementById('privacyIndicator');
        const statusDot = indicator.querySelector('.status-indicator');
        
        if (url.startsWith('https://')) {
            indicator.className = 'privacy-indicator';
            indicator.innerHTML = '<span class="status-indicator active"></span>Protected';
        } else if (url.startsWith('http://')) {
            indicator.className = 'privacy-indicator warning';
            indicator.innerHTML = '<span class="status-indicator warning"></span>Unsecured';
        } else {
            indicator.className = 'privacy-indicator';
            indicator.innerHTML = '<span class="status-indicator active"></span>Local';
        }
    }

    togglePrivacyPanel() {
        const panel = document.getElementById('privacyPanel');
        this.privacyPanelOpen = !this.privacyPanelOpen;
        panel.classList.toggle('open', this.privacyPanelOpen);
    }

    async togglePrivacySetting(toggle) {
        const setting = toggle.dataset.setting;
        const isActive = toggle.classList.contains('active');
        
        toggle.classList.toggle('active');
        
        try {
            if (window.phantomAPI) {
                const currentSettings = await window.phantomAPI.getPrivacySettings();
                
                // Map UI settings to privacy settings
                const settingMap = {
                    'canvasProtection': 'randomizeCanvasFingerprint',
                    'webglProtection': 'blockFingerprinting',
                    'audioProtection': 'blockFingerprinting',
                    'blockTrackers': 'blockTrackers',
                    'blockAds': 'blockAds',
                    'webrtcProtection': 'blockWebRTC'
                };

                if (settingMap[setting]) {
                    currentSettings[settingMap[setting]] = !isActive;
                    await window.phantomAPI.updatePrivacySettings(currentSettings);
                }

                // Handle special settings
                if (setting === 'userAgentRotation') {
                    if (!isActive) {
                        await window.phantomAPI.enableUserAgentRotation();
                    } else {
                        await window.phantomAPI.disableUserAgentRotation();
                    }
                }

                if (setting === 'proxyEnabled') {
                    if (!isActive) {
                        // Enable proxy with default settings
                        await window.phantomAPI.setProxy({
                            type: 'socks5',
                            host: '127.0.0.1',
                            port: 9050,
                            enabled: true
                        });
                    } else {
                        await window.phantomAPI.clearProxy();
                    }
                }

                if (setting === 'proxyRotation') {
                    if (!isActive) {
                        await window.phantomAPI.enableProxyRotation(10);
                    } else {
                        await window.phantomAPI.disableProxyRotation();
                    }
                }

                // Handle steganographic settings
                const steganographicSettings = {
                    'trafficObfuscation': 'enableTrafficObfuscation',
                    'timingRandomization': 'enableTimingRandomization',
                    'behaviorMasking': 'enableBehaviorMasking',
                    'decoyTraffic': 'enableDecoyTraffic',
                    'requestPadding': 'enableRequestPadding',
                    'dnsObfuscation': 'enableDNSObfuscation'
                };

                if (steganographicSettings[setting]) {
                    const currentSteganographicSettings = await window.phantomAPI.getSteganographicSettings();
                    currentSteganographicSettings[steganographicSettings[setting]] = !isActive;
                    await window.phantomAPI.updateSteganographicSettings(currentSteganographicSettings);
                }
            }
        } catch (error) {
            console.error('Failed to update privacy setting:', error);
            // Revert toggle state on error
            toggle.classList.toggle('active');
        }
    }

    async rotateUserAgent() {
        try {
            if (window.phantomAPI) {
                await window.phantomAPI.rotateUserAgent();
                this.showNotification('User agent rotated successfully');
            }
        } catch (error) {
            console.error('Failed to rotate user agent:', error);
            this.showNotification('Failed to rotate user agent', 'error');
        }
    }

    showLoading() {
        document.getElementById('loadingIndicator').style.display = 'block';
    }

    hideLoading() {
        document.getElementById('loadingIndicator').style.display = 'none';
    }

    showNotification(message, type = 'success') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            background: ${type === 'error' ? '#dc3545' : '#28a745'};
            color: white;
            border-radius: 4px;
            z-index: 10000;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        `;

        document.body.appendChild(notification);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    startStatsUpdater() {
        // Update privacy stats every 5 seconds
        setInterval(() => {
            this.updatePrivacyStats();
        }, 5000);
    }

    updatePrivacyStats() {
        // Simulate stats updates (in real implementation, get from main process)
        this.privacyStats.blockedTrackers += Math.floor(Math.random() * 3);
        this.privacyStats.blockedAds += Math.floor(Math.random() * 5);
        this.privacyStats.fingerprintAttempts += Math.floor(Math.random() * 2);
        this.privacyStats.dataLeaks += Math.floor(Math.random() * 1);
        this.privacyStats.decoyRequests += Math.floor(Math.random() * 4);
        this.privacyStats.obfuscatedPackets += Math.floor(Math.random() * 8);

        // Update UI
        document.getElementById('blockedTrackers').textContent = this.privacyStats.blockedTrackers;
        document.getElementById('blockedAds').textContent = this.privacyStats.blockedAds;
        document.getElementById('fingerprintAttempts').textContent = this.privacyStats.fingerprintAttempts;
        document.getElementById('dataLeaks').textContent = this.privacyStats.dataLeaks;
        document.getElementById('decoyRequests').textContent = this.privacyStats.decoyRequests;
        document.getElementById('obfuscatedPackets').textContent = this.privacyStats.obfuscatedPackets;
    }

    async updateObfuscationIntensity(intensity) {
        try {
            if (window.phantomAPI) {
                const currentSettings = await window.phantomAPI.getSteganographicSettings();
                currentSettings.obfuscationIntensity = intensity;
                await window.phantomAPI.updateSteganographicSettings(currentSettings);
                this.showNotification(`Obfuscation intensity set to ${intensity}`);
            }
        } catch (error) {
            console.error('Failed to update obfuscation intensity:', error);
            this.showNotification('Failed to update obfuscation intensity', 'error');
        }
    }

    async loadPrivacySettings() {
        try {
            if (window.phantomAPI) {
                const settings = await window.phantomAPI.getPrivacySettings();
                console.log('Privacy settings loaded:', settings);
            }
        } catch (error) {
            console.error('Failed to load privacy settings:', error);
        }
    }
}

// Initialize the browser UI when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.phantomBrowserUI = new PhantomBrowserUI();
    console.log('Phantom Browser UI initialized');

    // Add debugging tools to window for console access
    window.phantomDebug = {
        // Test API availability
        testAPI: async () => {
            console.log('=== API Availability Test ===');
            console.log('phantomAPI available:', !!window.phantomAPI);

            if (window.phantomAPI) {
                const methods = [
                    'getPrivacySettings',
                    'updatePrivacySettings',
                    'getSteganographicSettings',
                    'updateSteganographicSettings',
                    'navigate',
                    'enableUserAgentRotation',
                    'disableUserAgentRotation'
                ];

                for (const method of methods) {
                    console.log(`${method}:`, typeof window.phantomAPI[method]);
                }
            }
        },

        // Test privacy settings
        testPrivacySettings: async () => {
            console.log('=== Privacy Settings Test ===');
            try {
                const settings = await window.phantomAPI.getPrivacySettings();
                console.log('Current privacy settings:', settings);

                // Test update
                const testUpdate = { ...settings, blockTrackers: !settings.blockTrackers };
                console.log('Testing update with:', testUpdate);
                const result = await window.phantomAPI.updatePrivacySettings(testUpdate);
                console.log('Update result:', result);

                // Verify update
                const newSettings = await window.phantomAPI.getPrivacySettings();
                console.log('Settings after update:', newSettings);

            } catch (error) {
                console.error('Privacy settings test failed:', error);
            }
        },

        // Test navigation
        testNavigation: async (url = 'https://example.com') => {
            console.log('=== Navigation Test ===');
            try {
                console.log('Testing navigation to:', url);
                const result = await window.phantomAPI.navigate(url);
                console.log('Navigation result:', result);
            } catch (error) {
                console.error('Navigation test failed:', error);
            }
        },

        // Test webview
        testWebview: () => {
            console.log('=== Webview Test ===');
            const webview = document.getElementById('webview');
            console.log('Webview element:', webview);
            console.log('Webview src:', webview?.src);
            console.log('Webview ready state:', webview?.readyState);
        },

        // Test all toggles
        testAllToggles: async () => {
            console.log('=== Toggle Test ===');
            const toggles = document.querySelectorAll('.toggle-switch');
            console.log('Found toggles:', toggles.length);

            for (const toggle of toggles) {
                const setting = toggle.dataset.setting;
                const isActive = toggle.classList.contains('active');
                console.log(`Toggle ${setting}: ${isActive ? 'active' : 'inactive'}`);

                try {
                    console.log(`Testing toggle: ${setting}`);
                    await window.phantomBrowserUI.togglePrivacySetting(toggle);
                    console.log(`Toggle ${setting} completed successfully`);
                } catch (error) {
                    console.error(`Toggle ${setting} failed:`, error);
                }
            }
        },

        // Test search engine
        testSearchEngine: () => {
            console.log('=== Search Engine Test ===');
            const searchEngine = window.phantomBrowserUI.searchEngine;
            if (searchEngine) {
                console.log('Search engine available:', true);
                console.log('Available providers:', searchEngine.getProviders().map(p => p.name));
                console.log('Default provider:', searchEngine.getDefaultProvider().name);
                console.log('Settings:', searchEngine.getSettings());

                // Test search
                const testQuery = 'test search';
                const result = searchEngine.processInput(testQuery);
                console.log(`Search result for "${testQuery}":`, result);

                // Test URL detection
                const testUrl = 'example.com';
                const urlResult = searchEngine.processInput(testUrl);
                console.log(`URL result for "${testUrl}":`, urlResult);
            } else {
                console.error('Search engine not available');
            }
        },

        // Test search suggestions
        testSearchSuggestions: async (query = 'test') => {
            console.log('=== Search Suggestions Test ===');
            const searchEngine = window.phantomBrowserUI.searchEngine;
            if (searchEngine) {
                try {
                    const suggestions = await searchEngine.getSuggestions(query);
                    console.log(`Suggestions for "${query}":`, suggestions);
                } catch (error) {
                    console.error('Failed to get suggestions:', error);
                }
            } else {
                console.error('Search engine not available');
            }
        },

        // Test behavioral obfuscation fix
        testAutoTypingFix: () => {
            console.log('=== Auto-Typing Fix Test ===');
            const addressBar = document.getElementById('addressBar');
            if (addressBar) {
                console.log('Address bar found');
                console.log('Address bar ID:', addressBar.id);
                console.log('Address bar classes:', addressBar.className);
                console.log('Address bar type:', addressBar.type);

                // Focus on address bar to test if auto-typing still occurs
                addressBar.focus();
                console.log('Address bar focused - check if auto-typing occurs');

                setTimeout(() => {
                    console.log('Address bar value after 5 seconds:', addressBar.value);
                }, 5000);
            } else {
                console.error('Address bar not found');
            }
        }
    };

    console.log('Debug tools available at window.phantomDebug');
    console.log('Available commands:');
    console.log('- phantomDebug.testAPI()');
    console.log('- phantomDebug.testPrivacySettings()');
    console.log('- phantomDebug.testNavigation("https://example.com")');
    console.log('- phantomDebug.testWebview()');
    console.log('- phantomDebug.testAllToggles()');
    console.log('- phantomDebug.testSearchEngine()');
    console.log('- phantomDebug.testSearchSuggestions("query")');
    console.log('- phantomDebug.testAutoTypingFix()');
});
