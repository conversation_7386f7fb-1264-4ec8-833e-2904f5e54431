{"version": 3, "file": "BlockchainCoordinator.js", "sourceRoot": "", "sources": ["../../src/network/BlockchainCoordinator.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAmCjC,MAAa,qBAAqB;IAS9B,YAAY,MAAc,EAAE,UAAkB;QARtC,UAAK,GAAY,EAAE,CAAC;QACpB,wBAAmB,GAAuB,EAAE,CAAC;QAC7C,mBAAc,GAAkC,IAAI,GAAG,EAAE,CAAC;QAG1D,eAAU,GAAgB,IAAI,GAAG,EAAE,CAAC;QACpC,eAAU,GAAW,CAAC,CAAC,CAAC,2BAA2B;QAGvD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC9B,CAAC;IAEO,kBAAkB;QACtB,MAAM,WAAW,GAAqB;YAClC,IAAI,EAAE,kBAAkB;YACxB,MAAM,EAAE,SAAS;YACjB,OAAO,EAAE,EAAE,OAAO,EAAE,+BAA+B,EAAE;YACrD,SAAS,EAAE,EAAE;SAChB,CAAC;QAEF,MAAM,YAAY,GAAU;YACxB,KAAK,EAAE,CAAC;YACR,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,IAAI,EAAE,WAAW;YACjB,YAAY,EAAE,GAAG;YACjB,IAAI,EAAE,EAAE;YACR,KAAK,EAAE,CAAC;YACR,SAAS,EAAE,SAAS;SACvB,CAAC;QAEF,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;QACrD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC9B,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;IACzC,CAAC;IAED,cAAc,CAAC,IAAsB;QACjC,iCAAiC;QACjC,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC;YAC7B,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpC,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,CAAC,IAAI,SAAS,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;QACvE,CAAC;aAAM,CAAC;YACJ,OAAO,CAAC,IAAI,CAAC,0CAA0C,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;QAC1E,CAAC;IACL,CAAC;IAEO,eAAe,CAAC,IAAsB;QAC1C,oCAAoC;QACpC,6EAA6E;QAC7E,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;IACrC,CAAC;IAED,KAAK,CAAC,SAAS;QACX,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAC5C,MAAM,QAAQ,GAAU;YACpB,KAAK,EAAE,aAAa,CAAC,KAAK,GAAG,CAAC;YAC9B,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,IAAI,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE,yBAAyB;YAC5D,YAAY,EAAE,aAAa,CAAC,IAAI;YAChC,IAAI,EAAE,EAAE;YACR,KAAK,EAAE,CAAC;YACR,SAAS,EAAE,IAAI,CAAC,MAAM;SACzB,CAAC;QAEF,uBAAuB;QACvB,QAAQ,CAAC,IAAI,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAE9D,8BAA8B;QAC9B,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC;QAEjC,OAAO,QAAQ,CAAC;IACpB,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAAC,KAAY;QAC/C,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAE3C,OAAO,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAEvC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,KAAK,MAAM,EAAE,CAAC;gBAChD,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,YAAY,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;gBAC5D,OAAO,IAAI,CAAC;YAChB,CAAC;YAED,KAAK,CAAC,KAAK,EAAE,CAAC;YAEd,iDAAiD;YACjD,IAAI,KAAK,CAAC,KAAK,GAAG,IAAI,KAAK,CAAC,EAAE,CAAC;gBAC3B,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;YACzD,CAAC;QACL,CAAC;IACL,CAAC;IAEO,aAAa,CAAC,KAAY;QAC9B,MAAM,SAAS,GAAG,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;QACvI,OAAO,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACvE,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,KAAY;QAC3B,8BAA8B;QAC9B,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;YAC5B,OAAO,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YACvC,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,0BAA0B;QAC1B,MAAM,SAAS,GAAqB;YAChC,SAAS,EAAE,KAAK,CAAC,IAAI;YACrB,KAAK,EAAE,EAAE;YACT,MAAM,EAAE,SAAS;YACjB,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,EAAE,gBAAgB;SACtE,CAAC;QAEF,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QAE/C,wCAAwC;QACxC,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;YACnC,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QAClD,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,iCAAiC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;QAC3D,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,SAAiB,EAAE,IAA0B;QAC3D,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACrD,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YAC/C,OAAO,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YAC1C,OAAO;QACX,CAAC;QAED,8BAA8B;QAC9B,MAAM,YAAY,GAAG,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC;QACzE,IAAI,YAAY,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;YACjD,OAAO;QACX,CAAC;QAED,MAAM,QAAQ,GAAkB;YAC5B,SAAS;YACT,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,IAAI;YACJ,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC;SAC5C,CAAC;QAEF,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAE/B,gCAAgC;QAChC,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;IACzC,CAAC;IAEO,QAAQ,CAAC,SAAiB,EAAE,IAAY;QAC5C,MAAM,QAAQ,GAAG,GAAG,SAAS,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,EAAE,CAAC;QACrD,OAAO,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACxG,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,SAAiB;QAC1C,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACrD,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,MAAM,KAAK,SAAS;YAAE,OAAO;QAEzD,MAAM,YAAY,GAAG,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,MAAM,CAAC;QAC9E,MAAM,WAAW,GAAG,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,MAAM,CAAC;QAE5E,IAAI,YAAY,IAAI,SAAS,CAAC,SAAS,EAAE,CAAC;YACtC,SAAS,CAAC,MAAM,GAAG,UAAU,CAAC;YAC9B,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACnC,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;YACpC,OAAO,CAAC,GAAG,CAAC,SAAS,SAAS,wBAAwB,CAAC,CAAC;QAC5D,CAAC;aAAM,IAAI,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC;YAClE,SAAS,CAAC,MAAM,GAAG,UAAU,CAAC;YAC9B,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACnC,OAAO,CAAC,GAAG,CAAC,SAAS,SAAS,wBAAwB,CAAC,CAAC;QAC5D,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,SAAiB;QACzC,yCAAyC;QACzC,oEAAoE;QACpE,OAAO,CAAC,GAAG,CAAC,qBAAqB,SAAS,EAAE,CAAC,CAAC;QAE9C,6CAA6C;QAC7C,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACrD,IAAI,SAAS,EAAE,CAAC;YACZ,mCAAmC;YACnC,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;QACvE,CAAC;IACL,CAAC;IAEO,YAAY,CAAC,KAAY;QAC7B,oCAAoC;QACpC,MAAM,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QACjD,IAAI,cAAc,KAAK,KAAK,CAAC,IAAI,EAAE,CAAC;YAChC,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,yBAAyB;QACzB,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC3C,IAAI,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,KAAK,MAAM,EAAE,CAAC;YACtD,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,yBAAyB;QACzB,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAC5C,IAAI,KAAK,CAAC,YAAY,KAAK,aAAa,CAAC,IAAI,EAAE,CAAC;YAC5C,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,iBAAiB;QACjB,IAAI,KAAK,CAAC,KAAK,KAAK,aAAa,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC;YAC1C,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,cAAc;QACV,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC7C,CAAC;IAED,QAAQ;QACJ,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;IAC3B,CAAC;IAED,YAAY;QACR,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACzC,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAExC,IAAI,YAAY,CAAC,IAAI,KAAK,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,EAAE,CAAC;gBACzD,OAAO,KAAK,CAAC;YACjB,CAAC;YAED,IAAI,YAAY,CAAC,YAAY,KAAK,aAAa,CAAC,IAAI,EAAE,CAAC;gBACnD,OAAO,KAAK,CAAC;YACjB,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,YAAY,CAAC,MAAc;QACvB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC5B,OAAO,CAAC,GAAG,CAAC,oBAAoB,MAAM,EAAE,CAAC,CAAC;IAC9C,CAAC;IAED,eAAe,CAAC,MAAc;QAC1B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAC/B,OAAO,CAAC,GAAG,CAAC,sBAAsB,MAAM,EAAE,CAAC,CAAC;IAChD,CAAC;IAED,aAAa;QACT,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACvC,CAAC;IAED,sBAAsB;QAClB,OAAO,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC;IACzC,CAAC;IAED,kBAAkB,CAAC,SAAiB;QAChC,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC;IACtD,CAAC;IAED,gCAAgC;IAChC,KAAK,CAAC,uBAAuB,CAAC,SAAiB,EAAE,YAAsB,EAAE,SAAiB;QACtF,MAAM,gBAAgB,GAAqB;YACvC,IAAI,EAAE,mBAAmB;YACzB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,OAAO,EAAE;gBACL,SAAS;gBACT,YAAY;gBACZ,SAAS;gBACT,UAAU,EAAE,IAAI,CAAC,MAAM;aAC1B;YACD,SAAS,EAAE,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,EAAE,EAAE,SAAS,EAAE,YAAY,EAAE,SAAS,EAAE,CAAC;SACpG,CAAC;QAEF,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;QAEtC,qCAAqC;QACrC,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;QACrC,IAAI,KAAK,EAAE,CAAC;YACR,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC/B,OAAO,KAAK,CAAC,IAAI,CAAC;QACtB,CAAC;QAED,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;IAClE,CAAC;IAEO,oBAAoB,CAAC,IAAY,EAAE,OAAY;QACnD,MAAM,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC;QAC/D,OAAO,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACpG,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,gBAAqB;QACxC,MAAM,gBAAgB,GAAqB;YACvC,IAAI,EAAE,WAAW;YACjB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,OAAO,EAAE;gBACL,YAAY,EAAE,gBAAgB;gBAC9B,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACxB;YACD,SAAS,EAAE,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,gBAAgB,CAAC;SACtE,CAAC;QAEF,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;IAC1C,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,MAAc;QAClC,MAAM,gBAAgB,GAAqB;YACvC,IAAI,EAAE,YAAY;YAClB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,OAAO,EAAE;gBACL,MAAM;gBACN,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACxB;YACD,SAAS,EAAE,IAAI,CAAC,oBAAoB,CAAC,YAAY,EAAE,EAAE,MAAM,EAAE,CAAC;SACjE,CAAC;QAEF,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;IAC1C,CAAC;IAED,eAAe;QAOX,OAAO;YACH,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;YAC9B,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,CAAC,MAAM;YACpD,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI;YAChC,gBAAgB,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,MAAM;YACrG,OAAO,EAAE,IAAI,CAAC,YAAY,EAAE;SAC/B,CAAC;IACN,CAAC;IAED,8BAA8B;IAC9B,mBAAmB;QACf,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC;QAExC,KAAK,MAAM,CAAC,SAAS,EAAE,SAAS,CAAC,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACvD,IAAI,SAAS,CAAC,WAAW,IAAI,SAAS,CAAC,WAAW,GAAG,UAAU,EAAE,CAAC;gBAC9D,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAC1C,CAAC;QACL,CAAC;IACL,CAAC;CACJ;AAlWD,sDAkWC"}