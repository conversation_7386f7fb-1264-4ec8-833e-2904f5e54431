export interface LatticeParameters {
    dimension: number;
    modulus: number;
    errorDistribution: number;
    securityLevel: number;
}
export interface HashTreeNode {
    hash: Buffer;
    left?: HashTreeNode;
    right?: HashTreeNode;
    isLeaf: boolean;
    index?: number;
}
export interface KyberKeyPair {
    publicKey: {
        polynomialMatrix: number[][];
        errorVector: number[];
        modulus: number;
    };
    privateKey: {
        secretVector: number[];
        errorVector: number[];
    };
}
export interface DilithiumSignature {
    signature: Buffer;
    challenge: Buffer;
    response: Buffer;
    commitment: Buffer;
}
export interface SPHINCSKeyPair {
    publicKey: {
        rootHash: Buffer;
        publicSeed: Buffer;
    };
    privateKey: {
        secretSeed: Buffer;
        publicSeed: Buffer;
        merkleTree: HashTreeNode;
    };
}
export declare class PostQuantumAlgorithms {
    private static readonly KYBER_MODULUS;
    private static readonly KYBER_DIMENSIONS;
    private static readonly DILITHIUM_MODULUS;
    private static readonly SPHINCS_TREE_HEIGHT;
    static generateKyberKeyPair(securityLevel: 1 | 3 | 5): KyberKeyPair;
    static kyberEncapsulate(publicKey: KyberKeyPair['publicKey'], message: Buffer): {
        ciphertext: Buffer;
        sharedSecret: Buffer;
    };
    static kyberDecapsulate(privateKey: KyberKeyPair['privateKey'], ciphertext: Buffer): Buffer;
    static generateDilithiumKeyPair(securityLevel: 1 | 3 | 5): {
        publicKey: Buffer;
        privateKey: Buffer;
    };
    static dilithiumSign(message: Buffer, privateKey: Buffer): DilithiumSignature;
    static dilithiumVerify(message: Buffer, signature: DilithiumSignature, publicKey: Buffer): boolean;
    static generateSPHINCSKeyPair(): SPHINCSKeyPair;
    static sphincsSign(message: Buffer, privateKey: SPHINCSKeyPair['privateKey']): {
        signature: Buffer;
        authPath: Buffer[];
        leafIndex: number;
    };
    static sphincsVerify(message: Buffer, signature: {
        signature: Buffer;
        authPath: Buffer[];
        leafIndex: number;
    }, publicKey: SPHINCSKeyPair['publicKey']): boolean;
    private static generateSmallVector;
    private static generateRandomMatrix;
    private static matrixVectorMultiply;
    private static vectorDotProduct;
    private static transposeMatrix;
    private static encodeMessage;
    private static serializeMatrix;
    private static buildMerkleTree;
    private static generateOTSKey;
    private static createOTSSignature;
    private static recoverOTSPublicKey;
    private static getAuthenticationPath;
    private static computeRootFromPath;
}
//# sourceMappingURL=PostQuantumAlgorithms.d.ts.map