{"version": 3, "file": "PrivacyManager.js", "sourceRoot": "", "sources": ["../../src/privacy/PrivacyManager.ts"], "names": [], "mappings": ";;;AAAA,uCAAgD;AAgBhD,MAAa,cAAc;IAKvB;QACI,IAAI,CAAC,QAAQ,GAAG;YACZ,aAAa,EAAE,IAAI;YACnB,QAAQ,EAAE,IAAI;YACd,mBAAmB,EAAE,IAAI;YACzB,aAAa,EAAE,IAAI;YACnB,aAAa,EAAE,IAAI;YACnB,WAAW,EAAE,IAAI;YACjB,0BAA0B,EAAE,IAAI;YAChC,WAAW,EAAE,IAAI;YACjB,kBAAkB,EAAE,IAAI;YACxB,MAAM,EAAE,IAAI;SACf,CAAC;QAEF,IAAI,CAAC,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;QACjC,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;IAC3B,CAAC;IAED,KAAK,CAAC,UAAU;QACZ,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;QAC5B,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9B,IAAI,CAAC,wBAAwB,EAAE,CAAC;QAChC,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAChC,CAAC;IAEO,KAAK,CAAC,cAAc;QACxB,wBAAwB;QACxB,MAAM,eAAe,GAAG;YACpB,sBAAsB;YACtB,sBAAsB;YACtB,cAAc;YACd,iBAAiB;YACjB,uBAAuB;YACvB,qBAAqB;YACrB,qBAAqB;YACrB,uBAAuB;YACvB,gBAAgB;YAChB,cAAc;YACd,aAAa;YACb,aAAa;YACb,eAAe;YACf,eAAe;YACf,YAAY;YACZ,eAAe;YACf,eAAe;YACf,cAAc;YACd,cAAc;YACd,aAAa;YACb,eAAe;SAClB,CAAC;QAEF,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;QAEpE,mDAAmD;QACnD,IAAI,CAAC,YAAY,GAAG;YAChB,yBAAyB;YACzB,0BAA0B;YAC1B,oBAAoB;YACpB,wBAAwB;YACxB,oBAAoB;YACpB,yBAAyB;YACzB,yBAAyB;YACzB,yBAAyB;SAC5B,CAAC;IACN,CAAC;IAEO,KAAK,CAAC,gBAAgB;QAC1B,MAAM,GAAG,GAAG,kBAAO,CAAC,cAAc,CAAC;QAEnC,2BAA2B;QAC3B,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;YACvB,GAAG,CAAC,QAAQ,CAAC;gBACT,IAAI,EAAE,YAAY;gBAClB,SAAS,EAAE,IAAI,CAAC,iBAAiB,EAAE;aACtC,CAAC,CAAC;QACP,CAAC;QAED,4BAA4B;QAC5B,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC;YACZ,GAAG,EAAE,qBAAqB;YAC1B,IAAI,EAAE,UAAU;YAChB,KAAK,EAAE,QAAQ;SAClB,CAAC,CAAC;QAEH,uBAAuB;QACvB,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC,CAAC;IACrD,CAAC;IAEO,wBAAwB;QAC5B,MAAM,GAAG,GAAG,kBAAO,CAAC,cAAc,CAAC;QAEnC,0BAA0B;QAC1B,GAAG,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE,IAAI,EAAE,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE;YAC3E,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAEjC,sCAAsC;YACtC,IAAI,IAAI,CAAC,QAAQ,CAAC,aAAa,IAAI,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACrE,QAAQ,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;gBAC3B,OAAO;YACX,CAAC;YAED,uBAAuB;YACvB,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;gBACjE,QAAQ,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;gBAC3B,OAAO;YACX,CAAC;YAED,QAAQ,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;QAEH,6BAA6B;QAC7B,GAAG,CAAC,UAAU,CAAC,mBAAmB,CAAC,EAAE,IAAI,EAAE,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE;YAC/E,MAAM,OAAO,GAAG,OAAO,CAAC,cAAc,CAAC;YAEvC,0BAA0B;YAC1B,OAAO,OAAO,CAAC,kBAAkB,CAAC,CAAC;YACnC,OAAO,OAAO,CAAC,iBAAiB,CAAC,CAAC;YAClC,OAAO,OAAO,CAAC,WAAW,CAAC,CAAC;YAE5B,iBAAiB;YACjB,IAAI,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;gBACrB,OAAO,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YACzD,CAAC;YAED,sBAAsB;YACtB,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;YACrB,OAAO,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC;YAEzB,QAAQ,CAAC,EAAE,cAAc,EAAE,OAAO,EAAE,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,oBAAoB;QACxB,MAAM,GAAG,GAAG,kBAAO,CAAC,cAAc,CAAC;QAEnC,oCAAoC;QACpC,GAAG,CAAC,UAAU,CAAC,iBAAiB,CAAC,EAAE,IAAI,EAAE,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE;YAC7E,IAAI,OAAO,CAAC,eAAe,IAAI,OAAO,CAAC,eAAe,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;gBACjG,0CAA0C;gBAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;gBAC/C,OAAO,CAAC,eAAe,CAAC,kBAAkB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAC3D,CAAC;YAED,QAAQ,CAAC,EAAE,eAAe,EAAE,OAAO,CAAC,eAAe,EAAE,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,gBAAgB,CAAC,QAAgB;QACrC,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAClD,QAAQ,KAAK,MAAM,IAAI,QAAQ,CAAC,QAAQ,CAAC,GAAG,GAAG,MAAM,CAAC,CACzD,CAAC;IACN,CAAC;IAEO,kBAAkB,CAAC,GAAW;QAClC,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACjC,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC9C,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACjC,OAAO,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAChC,CAAC;YACD,OAAO,KAAK,CAAC;QACjB,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,aAAa,CAAC,GAAW;QAC7B,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;YAC5B,OAAO,GAAG,MAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,QAAQ,GAAG,CAAC;QACrD,CAAC;QAAC,MAAM,CAAC;YACL,OAAO,yBAAyB,CAAC;QACrC,CAAC;IACL,CAAC;IAEO,uBAAuB;QAC3B,MAAM,UAAU,GAAG;YACf,iHAAiH;YACjH,uHAAuH;YACvH,uGAAuG;YACvG,kFAAkF;YAClF,sFAAsF;SACzF,CAAC;QAEF,OAAO,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;IACrE,CAAC;IAEO,iBAAiB;QACrB,OAAO;;;;;;;;;;SAUN,CAAC;IACN,CAAC;IAEO,wBAAwB;QAC5B,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SAuCN,CAAC;IACN,CAAC;IAED,cAAc,CAAC,WAAqC;QAChD,IAAI,CAAC,QAAQ,GAAG,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,WAAW,EAAE,CAAC;IACzD,CAAC;IAED,WAAW;QACP,OAAO,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;IAChC,CAAC;IAED,KAAK,CAAC,gBAAgB;QAClB,MAAM,GAAG,GAAG,kBAAO,CAAC,cAAc,CAAC;QACnC,MAAM,GAAG,CAAC,gBAAgB,EAAE,CAAC;QAC7B,MAAM,GAAG,CAAC,UAAU,EAAE,CAAC;QACvB,MAAM,GAAG,CAAC,cAAc,EAAE,CAAC;IAC/B,CAAC;CACJ;AApQD,wCAoQC"}