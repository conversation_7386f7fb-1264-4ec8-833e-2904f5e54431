{"version": 3, "file": "MobilePlatformAdapter.d.ts", "sourceRoot": "", "sources": ["../../src/crossplatform/MobilePlatformAdapter.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AAGtC,MAAM,WAAW,gBAAgB;IAC7B,QAAQ,EAAE,MAAM,CAAC;IACjB,QAAQ,EAAE,SAAS,GAAG,KAAK,CAAC;IAC5B,SAAS,EAAE,MAAM,CAAC;IAClB,WAAW,EAAE,MAAM,CAAC;IACpB,gBAAgB,EAAE;QAAE,KAAK,EAAE,MAAM,CAAC;QAAC,MAAM,EAAE,MAAM,CAAA;KAAE,CAAC;IACpD,YAAY,EAAE,MAAM,CAAC;IACrB,WAAW,EAAE,MAAM,GAAG,UAAU,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;IACtD,QAAQ,EAAE;QACN,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB,SAAS,EAAE,MAAM,CAAC;KACrB,CAAC;IACF,YAAY,EAAE;QACV,OAAO,EAAE,MAAM,EAAE,CAAC;QAClB,mBAAmB,EAAE,MAAM,EAAE,CAAC;QAC9B,cAAc,EAAE,MAAM,EAAE,CAAC;QACzB,sBAAsB,EAAE,MAAM,EAAE,CAAC;KACpC,CAAC;CACL;AAED,MAAM,WAAW,sBAAsB;IACnC,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,cAAc,GAAG,eAAe,GAAG,iBAAiB,GAAG,mBAAmB,GAAG,kBAAkB,CAAC;IACtG,QAAQ,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,UAAU,CAAC;IACjD,aAAa,EAAE,SAAS,GAAG,KAAK,GAAG,QAAQ,GAAG,MAAM,CAAC;IACrD,YAAY,EAAE,MAAM,CAAC;IACrB,QAAQ,EAAE,MAAM,CAAC;IACjB,UAAU,EAAE;QACR,OAAO,CAAC,EAAE,MAAM,EAAE,CAAC;QACnB,SAAS,EAAE,MAAM,CAAC;QAClB,QAAQ,EAAE,MAAM,CAAC;QACjB,mBAAmB,EAAE;YACjB,YAAY,EAAE,OAAO,CAAC;YACtB,iBAAiB,EAAE,OAAO,CAAC;YAC3B,cAAc,EAAE,OAAO,CAAC;YACxB,gBAAgB,EAAE,OAAO,CAAC;SAC7B,CAAC;KACL,CAAC;CACL;AAED,MAAM,WAAW,gBAAgB;IAC7B,WAAW,EAAE,MAAM,CAAC;IACpB,OAAO,EAAE,MAAM,CAAC;IAChB,QAAQ,EAAE,QAAQ,GAAG,cAAc,GAAG,eAAe,GAAG,MAAM,GAAG,UAAU,GAAG,SAAS,CAAC;IACxF,YAAY,EAAE;QACV,sBAAsB,EAAE,MAAM,CAAC;QAC/B,iBAAiB,EAAE,MAAM,CAAC;QAC1B,cAAc,EAAE,MAAM,EAAE,CAAC;QACzB,kBAAkB,EAAE,OAAO,CAAC;KAC/B,CAAC;IACF,eAAe,EAAE;QACb,mBAAmB,EAAE,MAAM,CAAC;QAC5B,gBAAgB,EAAE,MAAM,CAAC;QACzB,gBAAgB,EAAE,MAAM,EAAE,CAAC;KAC9B,CAAC;CACL;AAED,MAAM,WAAW,gBAAgB;IAC7B,aAAa,CAAC,EAAE;QAAE,CAAC,EAAE,MAAM,CAAC;QAAC,CAAC,EAAE,MAAM,CAAC;QAAC,CAAC,EAAE,MAAM,CAAA;KAAE,CAAC;IACpD,SAAS,CAAC,EAAE;QAAE,CAAC,EAAE,MAAM,CAAC;QAAC,CAAC,EAAE,MAAM,CAAC;QAAC,CAAC,EAAE,MAAM,CAAA;KAAE,CAAC;IAChD,YAAY,CAAC,EAAE;QAAE,CAAC,EAAE,MAAM,CAAC;QAAC,CAAC,EAAE,MAAM,CAAC;QAAC,CAAC,EAAE,MAAM,CAAA;KAAE,CAAC;IACnD,GAAG,CAAC,EAAE;QAAE,QAAQ,EAAE,MAAM,CAAC;QAAC,SAAS,EAAE,MAAM,CAAC;QAAC,QAAQ,EAAE,MAAM,CAAA;KAAE,CAAC;IAChE,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,SAAS,EAAE,MAAM,CAAC;CACrB;AAED,qBAAa,qBAAsB,SAAQ,YAAY;IACnD,OAAO,CAAC,UAAU,CAAmB;IACrC,OAAO,CAAC,WAAW,CAA4C;IAC/D,OAAO,CAAC,WAAW,CAAkD;IACrE,OAAO,CAAC,eAAe,CAAwB;IAC/C,OAAO,CAAC,cAAc,CAA6B;IACnD,OAAO,CAAC,cAAc,CAAuB;;IAWvC,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IAgBjC,OAAO,CAAC,kBAAkB;IAmC1B,OAAO,CAAC,qBAAqB;IAsFvB,6BAA6B,CAAC,IAAI,EAAE,sBAAsB,GAAG,OAAO,CAAC,IAAI,CAAC;YAqClE,mBAAmB;YAkCnB,oBAAoB;YAmBpB,gBAAgB;YA4BhB,sBAAsB;YAiCtB,sBAAsB;YAkBtB,kBAAkB;YAiBlB,uBAAuB;IAoCrC,OAAO,CAAC,kBAAkB;IAQ1B,OAAO,CAAC,sBAAsB;IAU9B,OAAO,CAAC,sBAAsB;IAW9B,OAAO,CAAC,qBAAqB;IAY7B,mBAAmB,IAAI,gBAAgB;IAIvC,kBAAkB,IAAI,MAAM;IAI5B,4BAA4B,IAAI;QAC5B,YAAY,EAAE,MAAM,CAAC;QACrB,mBAAmB,EAAE,OAAO,CAAC;QAC7B,oBAAoB,EAAE,OAAO,CAAC;KACjC;IAID,OAAO,IAAI,IAAI;CAMlB"}