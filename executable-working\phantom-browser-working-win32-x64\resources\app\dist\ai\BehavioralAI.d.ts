export interface BehavioralDataPoint {
    timestamp: number;
    action: string;
    duration: number;
    context: {
        timeOfDay: number;
        dayOfWeek: number;
        sessionLength: number;
        previousActions: string[];
    };
    metrics: {
        mouseVelocity?: number;
        scrollSpeed?: number;
        typingCadence?: number;
        pauseDuration?: number;
    };
}
export interface BehavioralPattern {
    id: string;
    name: string;
    confidence: number;
    features: {
        avgActionInterval: number;
        preferredTimeSlots: number[];
        commonSequences: string[][];
        typingProfile: TypingProfile;
        mouseProfile: MouseProfile;
        scrollProfile: ScrollProfile;
    };
    adaptationRate: number;
    lastUpdated: number;
}
export interface TypingProfile {
    avgSpeed: number;
    burstPatterns: number[];
    pausePatterns: number[];
    errorRate: number;
    correctionDelay: number;
}
export interface MouseProfile {
    avgVelocity: number;
    accelerationPattern: number[];
    jitterLevel: number;
    clickPrecision: number;
    dwellTime: number;
}
export interface ScrollProfile {
    avgSpeed: number;
    scrollPattern: 'smooth' | 'jerky' | 'burst' | 'variable';
    directionBias: number;
    pauseFrequency: number;
}
export declare class BehavioralAI {
    private trainingData;
    private learnedPatterns;
    private currentPattern;
    private neuralNetwork;
    private adaptationThreshold;
    private maxTrainingData;
    constructor();
    initialize(): Promise<void>;
    private initializeBasePatterns;
    recordBehavior(action: string, duration: number, metrics?: any): void;
    private loadTrainingData;
    private generateSyntheticTrainingData;
    private generateRealisticDuration;
    private generateRealisticMetrics;
    private generateRandomActionSequence;
    private trainInitialModel;
    private prepareTrainingSet;
    private extractFeatures;
    private encodeAction;
    private encodeActionSequence;
    private startContinuousLearning;
    private adaptBehavior;
    private analyzePatterns;
    private extractActionSequences;
    private findPreferredTimeSlots;
    private analyzeTypingProfile;
    private analyzeMouseProfile;
    private analyzeScrollProfile;
    private calculateAverage;
    private calculatePauseBetweenActions;
    private shouldAdaptToPattern;
    private updateBehavioralModel;
    predictNextAction(context: any): {
        action: string;
        confidence: number;
        timing: number;
    };
    private extractContextFeatures;
    private predictTiming;
    getCurrentPattern(): BehavioralPattern | null;
    getSessionLength(): number;
    private getRecentActions;
    getAdaptationMetrics(): {
        totalPatterns: number;
        currentConfidence: number;
        adaptationRate: number;
        trainingDataSize: number;
    };
}
//# sourceMappingURL=BehavioralAI.d.ts.map